"""
User-related database models.
"""

from sqlalchemy import (
    <PERSON>umn, String, <PERSON>olean, DateTime, Text, Integer, 
    ForeignKey, JSON, Float
)
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
import uuid
from app.database import Base


class User(Base):
    """User account model."""
    
    __tablename__ = "users"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    email = Column(String(255), unique=True, index=True, nullable=False)
    hashed_password = Column(String(255), nullable=False)
    full_name = Column(String(255), nullable=False)
    is_active = Column(Boolean, default=True)
    is_verified = Column(Boolean, default=False)
    is_superuser = Column(Boolean, default=False)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    last_login = Column(DateTime(timezone=True))
    
    # Relationships
    profile = relationship("UserProfile", back_populates="user", uselist=False)
    settings = relationship("UserSettings", back_populates="user", uselist=False)
    tasks = relationship("Task", back_populates="user")
    integrations = relationship("Integration", back_populates="user")
    productivity_metrics = relationship("ProductivityMetric", back_populates="user")
    energy_patterns = relationship("EnergyPattern", back_populates="user")
    
    def __repr__(self):
        return f"<User(id={self.id}, email={self.email})>"


class UserProfile(Base):
    """Extended user profile information."""
    
    __tablename__ = "user_profiles"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    user_id = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=False)
    
    # Profile information
    avatar_url = Column(String(500))
    bio = Column(Text)
    timezone = Column(String(50), default="UTC")
    language = Column(String(10), default="en")
    country = Column(String(2))
    
    # Work preferences
    work_hours_start = Column(Integer, default=9)  # 24-hour format
    work_hours_end = Column(Integer, default=17)
    work_days = Column(JSON, default=["monday", "tuesday", "wednesday", "thursday", "friday"])
    
    # Productivity preferences
    focus_session_duration = Column(Integer, default=25)  # minutes
    break_duration = Column(Integer, default=5)  # minutes
    long_break_duration = Column(Integer, default=15)  # minutes
    
    # Energy patterns
    peak_energy_hours = Column(JSON)  # List of hours when user is most productive
    low_energy_hours = Column(JSON)   # List of hours when user has low energy
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    user = relationship("User", back_populates="profile")
    
    def __repr__(self):
        return f"<UserProfile(user_id={self.user_id}, timezone={self.timezone})>"


class UserSettings(Base):
    """User application settings and preferences."""
    
    __tablename__ = "user_settings"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    user_id = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=False)
    
    # Notification settings
    email_notifications = Column(Boolean, default=True)
    push_notifications = Column(Boolean, default=True)
    slack_notifications = Column(Boolean, default=False)
    
    # Task management settings
    auto_schedule_tasks = Column(Boolean, default=True)
    smart_batching = Column(Boolean, default=True)
    energy_based_scheduling = Column(Boolean, default=True)
    
    # AI assistant settings
    ai_suggestions = Column(Boolean, default=True)
    voice_commands = Column(Boolean, default=True)
    auto_categorization = Column(Boolean, default=True)
    
    # Privacy settings
    data_sharing = Column(Boolean, default=False)
    analytics_tracking = Column(Boolean, default=True)
    
    # Integration settings
    sync_frequency = Column(Integer, default=15)  # minutes
    auto_import_tasks = Column(Boolean, default=True)
    
    # Theme and UI settings
    theme = Column(String(20), default="light")  # light, dark, auto
    dashboard_layout = Column(JSON)
    
    # Advanced settings
    custom_settings = Column(JSON, default={})
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    user = relationship("User", back_populates="settings")
    
    def __repr__(self):
        return f"<UserSettings(user_id={self.user_id}, theme={self.theme})>"
