"""
Integration-related database models.
"""

from sqlalchemy import (
    <PERSON>umn, String, <PERSON>olean, DateTime, Text, Integer, 
    ForeignKey, JSON, Enum
)
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
import uuid
import enum
from app.database import Base


class IntegrationType(enum.Enum):
    """Integration service types."""
    SLACK = "slack"
    GOOGLE_WORKSPACE = "google_workspace"
    NOTION = "notion"
    GITHUB = "github"
    MICROSOFT_365 = "microsoft_365"
    TRELLO = "trello"
    ASANA = "asana"
    JIRA = "jira"


class IntegrationStatus(enum.Enum):
    """Integration status."""
    ACTIVE = "active"
    INACTIVE = "inactive"
    ERROR = "error"
    PENDING = "pending"


class Integration(Base):
    """User integration with external services."""
    
    __tablename__ = "integrations"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    user_id = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=False)
    
    # Integration details
    service_type = Column(Enum(IntegrationType), nullable=False)
    service_name = Column(String(100), nullable=False)
    status = Column(Enum(IntegrationStatus), default=IntegrationStatus.PENDING)
    
    # Authentication data (encrypted)
    access_token = Column(Text)
    refresh_token = Column(Text)
    token_expires_at = Column(DateTime(timezone=True))
    
    # Service-specific configuration
    workspace_id = Column(String(255))
    workspace_name = Column(String(255))
    user_email = Column(String(255))
    
    # Sync settings
    auto_sync = Column(Boolean, default=True)
    sync_frequency = Column(Integer, default=15)  # minutes
    last_sync_at = Column(DateTime(timezone=True))
    next_sync_at = Column(DateTime(timezone=True))
    
    # Error tracking
    error_count = Column(Integer, default=0)
    last_error = Column(Text)
    last_error_at = Column(DateTime(timezone=True))
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    user = relationship("User", back_populates="integrations")
    configs = relationship("IntegrationConfig", back_populates="integration")
    logs = relationship("IntegrationLog", back_populates="integration")
    
    def __repr__(self):
        return f"<Integration(id={self.id}, service={self.service_type}, status={self.status})>"


class IntegrationConfig(Base):
    """Configuration settings for integrations."""
    
    __tablename__ = "integration_configs"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    integration_id = Column(UUID(as_uuid=True), ForeignKey("integrations.id"), nullable=False)
    
    # Configuration key-value pairs
    config_key = Column(String(100), nullable=False)
    config_value = Column(Text)
    config_type = Column(String(20), default="string")  # string, boolean, integer, json
    
    # Metadata
    description = Column(Text)
    is_sensitive = Column(Boolean, default=False)  # For passwords, tokens, etc.
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    integration = relationship("Integration", back_populates="configs")
    
    def __repr__(self):
        return f"<IntegrationConfig(integration_id={self.integration_id}, key={self.config_key})>"


class IntegrationLog(Base):
    """Log entries for integration activities."""
    
    __tablename__ = "integration_logs"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    integration_id = Column(UUID(as_uuid=True), ForeignKey("integrations.id"), nullable=False)
    
    # Log details
    action = Column(String(100), nullable=False)  # sync, import, export, etc.
    status = Column(String(20), nullable=False)   # success, error, warning
    message = Column(Text)
    
    # Metrics
    items_processed = Column(Integer, default=0)
    items_created = Column(Integer, default=0)
    items_updated = Column(Integer, default=0)
    items_failed = Column(Integer, default=0)
    
    # Timing
    started_at = Column(DateTime(timezone=True))
    completed_at = Column(DateTime(timezone=True))
    duration_seconds = Column(Integer)
    
    # Error details
    error_code = Column(String(50))
    error_details = Column(JSON)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # Relationships
    integration = relationship("Integration", back_populates="logs")
    
    def __repr__(self):
        return f"<IntegrationLog(integration_id={self.integration_id}, action={self.action}, status={self.status})>"
