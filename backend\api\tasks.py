"""
Task management API endpoints.
"""

from datetime import datetime
from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_
from pydantic import BaseModel
import uuid

from app.database import get_db
from models.user import User
from models.task import Task, TaskStatus, TaskPriority, EnergyLevel, TaskCategory, TaskTag
from utils.auth import get_current_user
from services.ai_assistant import AIAssistantService
from services.task_scheduler import TaskSchedulerService

router = APIRouter()


# Pydantic models
class TaskCreate(BaseModel):
    title: str
    description: Optional[str] = None
    priority: TaskPriority = TaskPriority.MEDIUM
    due_date: Optional[datetime] = None
    estimated_duration: Optional[int] = None  # minutes
    energy_level_required: EnergyLevel = EnergyLevel.MEDIUM
    category_id: Optional[str] = None
    tags: Optional[List[str]] = []


class TaskUpdate(BaseModel):
    title: Optional[str] = None
    description: Optional[str] = None
    status: Optional[TaskStatus] = None
    priority: Optional[TaskPriority] = None
    due_date: Optional[datetime] = None
    estimated_duration: Optional[int] = None
    energy_level_required: Optional[EnergyLevel] = None
    progress_percentage: Optional[float] = None
    category_id: Optional[str] = None
    tags: Optional[List[str]] = None


class TaskResponse(BaseModel):
    id: str
    title: str
    description: Optional[str]
    status: TaskStatus
    priority: TaskPriority
    due_date: Optional[datetime]
    scheduled_start: Optional[datetime]
    scheduled_end: Optional[datetime]
    estimated_duration: Optional[int]
    actual_duration: Optional[int]
    energy_level_required: EnergyLevel
    progress_percentage: float
    time_spent: int
    created_at: datetime
    updated_at: Optional[datetime]
    category: Optional[dict] = None
    tags: List[dict] = []
    
    class Config:
        from_attributes = True


class TaskListResponse(BaseModel):
    tasks: List[TaskResponse]
    total: int
    limit: int
    offset: int


@router.get("/", response_model=TaskListResponse)
async def get_tasks(
    status: Optional[TaskStatus] = None,
    priority: Optional[TaskPriority] = None,
    category_id: Optional[str] = None,
    due_before: Optional[datetime] = None,
    due_after: Optional[datetime] = None,
    search: Optional[str] = None,
    limit: int = Query(50, le=100),
    offset: int = Query(0, ge=0),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get user's tasks with filtering and pagination."""
    
    # Build query
    query = db.query(Task).filter(Task.user_id == current_user.id)
    
    # Apply filters
    if status:
        query = query.filter(Task.status == status)
    
    if priority:
        query = query.filter(Task.priority == priority)
    
    if category_id:
        query = query.filter(Task.category_id == category_id)
    
    if due_before:
        query = query.filter(Task.due_date <= due_before)
    
    if due_after:
        query = query.filter(Task.due_date >= due_after)
    
    if search:
        search_filter = or_(
            Task.title.ilike(f"%{search}%"),
            Task.description.ilike(f"%{search}%")
        )
        query = query.filter(search_filter)
    
    # Get total count
    total = query.count()
    
    # Apply pagination and ordering
    tasks = query.order_by(Task.created_at.desc()).offset(offset).limit(limit).all()
    
    # Format response
    task_responses = []
    for task in tasks:
        task_dict = {
            "id": str(task.id),
            "title": task.title,
            "description": task.description,
            "status": task.status,
            "priority": task.priority,
            "due_date": task.due_date,
            "scheduled_start": task.scheduled_start,
            "scheduled_end": task.scheduled_end,
            "estimated_duration": task.estimated_duration,
            "actual_duration": task.actual_duration,
            "energy_level_required": task.energy_level_required,
            "progress_percentage": task.progress_percentage,
            "time_spent": task.time_spent,
            "created_at": task.created_at,
            "updated_at": task.updated_at,
            "category": {
                "id": str(task.category.id),
                "name": task.category.name,
                "color": task.category.color
            } if task.category else None,
            "tags": [
                {"id": str(tag.id), "name": tag.name, "color": tag.color}
                for tag in task.tags
            ]
        }
        task_responses.append(task_dict)
    
    return {
        "tasks": task_responses,
        "total": total,
        "limit": limit,
        "offset": offset
    }


@router.post("/", response_model=TaskResponse)
async def create_task(
    task_data: TaskCreate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Create a new task."""
    
    # Use AI to enhance task data
    ai_service = AIAssistantService()
    enhanced_data = await ai_service.enhance_task_data(task_data.dict())
    
    # Create task
    task = Task(
        user_id=current_user.id,
        title=task_data.title,
        description=task_data.description,
        priority=enhanced_data.get('priority', task_data.priority),
        due_date=task_data.due_date,
        estimated_duration=enhanced_data.get('estimated_duration', task_data.estimated_duration),
        energy_level_required=enhanced_data.get('energy_level_required', task_data.energy_level_required),
        category_id=task_data.category_id,
        ai_category_confidence=enhanced_data.get('category_confidence'),
        ai_priority_confidence=enhanced_data.get('priority_confidence'),
        ai_duration_estimate=enhanced_data.get('duration_estimate'),
        ai_tags=enhanced_data.get('suggested_tags', [])
    )
    
    db.add(task)
    db.commit()
    db.refresh(task)
    
    # Handle tags
    if task_data.tags:
        for tag_name in task_data.tags:
            # Find or create tag
            tag = db.query(TaskTag).filter(
                and_(TaskTag.user_id == current_user.id, TaskTag.name == tag_name)
            ).first()
            
            if not tag:
                tag = TaskTag(user_id=current_user.id, name=tag_name)
                db.add(tag)
                db.commit()
                db.refresh(tag)
            
            task.tags.append(tag)
    
    db.commit()
    
    # Schedule task using AI
    scheduler_service = TaskSchedulerService()
    await scheduler_service.schedule_task(task.id, current_user.id)
    
    # Refresh task to get updated scheduling info
    db.refresh(task)
    
    return TaskResponse(
        id=str(task.id),
        title=task.title,
        description=task.description,
        status=task.status,
        priority=task.priority,
        due_date=task.due_date,
        scheduled_start=task.scheduled_start,
        scheduled_end=task.scheduled_end,
        estimated_duration=task.estimated_duration,
        actual_duration=task.actual_duration,
        energy_level_required=task.energy_level_required,
        progress_percentage=task.progress_percentage,
        time_spent=task.time_spent,
        created_at=task.created_at,
        updated_at=task.updated_at,
        category={
            "id": str(task.category.id),
            "name": task.category.name,
            "color": task.category.color
        } if task.category else None,
        tags=[
            {"id": str(tag.id), "name": tag.name, "color": tag.color}
            for tag in task.tags
        ]
    )


@router.get("/{task_id}", response_model=TaskResponse)
async def get_task(
    task_id: str,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get a specific task."""
    
    task = db.query(Task).filter(
        and_(Task.id == task_id, Task.user_id == current_user.id)
    ).first()
    
    if not task:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Task not found"
        )
    
    return TaskResponse(
        id=str(task.id),
        title=task.title,
        description=task.description,
        status=task.status,
        priority=task.priority,
        due_date=task.due_date,
        scheduled_start=task.scheduled_start,
        scheduled_end=task.scheduled_end,
        estimated_duration=task.estimated_duration,
        actual_duration=task.actual_duration,
        energy_level_required=task.energy_level_required,
        progress_percentage=task.progress_percentage,
        time_spent=task.time_spent,
        created_at=task.created_at,
        updated_at=task.updated_at,
        category={
            "id": str(task.category.id),
            "name": task.category.name,
            "color": task.category.color
        } if task.category else None,
        tags=[
            {"id": str(tag.id), "name": tag.name, "color": tag.color}
            for tag in task.tags
        ]
    )


@router.put("/{task_id}", response_model=TaskResponse)
async def update_task(
    task_id: str,
    task_data: TaskUpdate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Update an existing task."""

    task = db.query(Task).filter(
        and_(Task.id == task_id, Task.user_id == current_user.id)
    ).first()

    if not task:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Task not found"
        )

    # Update task fields
    update_data = task_data.dict(exclude_unset=True)

    for field, value in update_data.items():
        if field == "tags":
            continue  # Handle tags separately
        setattr(task, field, value)

    # Handle status changes
    if task_data.status == TaskStatus.COMPLETED and task.status != TaskStatus.COMPLETED:
        task.completed_at = datetime.utcnow()
        task.progress_percentage = 100.0
    elif task_data.status == TaskStatus.CANCELLED and task.status != TaskStatus.CANCELLED:
        task.cancelled_at = datetime.utcnow()

    # Handle tags update
    if task_data.tags is not None:
        # Clear existing tags
        task.tags.clear()

        # Add new tags
        for tag_name in task_data.tags:
            tag = db.query(TaskTag).filter(
                and_(TaskTag.user_id == current_user.id, TaskTag.name == tag_name)
            ).first()

            if not tag:
                tag = TaskTag(user_id=current_user.id, name=tag_name)
                db.add(tag)
                db.commit()
                db.refresh(tag)

            task.tags.append(tag)

    task.updated_at = datetime.utcnow()
    db.commit()
    db.refresh(task)

    return TaskResponse(
        id=str(task.id),
        title=task.title,
        description=task.description,
        status=task.status,
        priority=task.priority,
        due_date=task.due_date,
        scheduled_start=task.scheduled_start,
        scheduled_end=task.scheduled_end,
        estimated_duration=task.estimated_duration,
        actual_duration=task.actual_duration,
        energy_level_required=task.energy_level_required,
        progress_percentage=task.progress_percentage,
        time_spent=task.time_spent,
        created_at=task.created_at,
        updated_at=task.updated_at,
        category={
            "id": str(task.category.id),
            "name": task.category.name,
            "color": task.category.color
        } if task.category else None,
        tags=[
            {"id": str(tag.id), "name": tag.name, "color": tag.color}
            for tag in task.tags
        ]
    )


@router.delete("/{task_id}")
async def delete_task(
    task_id: str,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Delete a task."""

    task = db.query(Task).filter(
        and_(Task.id == task_id, Task.user_id == current_user.id)
    ).first()

    if not task:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Task not found"
        )

    db.delete(task)
    db.commit()

    return {"message": "Task deleted successfully"}


@router.post("/{task_id}/start")
async def start_task(
    task_id: str,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Start working on a task."""

    task = db.query(Task).filter(
        and_(Task.id == task_id, Task.user_id == current_user.id)
    ).first()

    if not task:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Task not found"
        )

    if task.status == TaskStatus.COMPLETED:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Cannot start a completed task"
        )

    task.status = TaskStatus.IN_PROGRESS
    if not task.scheduled_start:
        task.scheduled_start = datetime.utcnow()

    db.commit()

    return {"message": "Task started successfully"}


@router.post("/{task_id}/complete")
async def complete_task(
    task_id: str,
    actual_duration: Optional[int] = None,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Mark a task as completed."""

    task = db.query(Task).filter(
        and_(Task.id == task_id, Task.user_id == current_user.id)
    ).first()

    if not task:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Task not found"
        )

    if task.status == TaskStatus.COMPLETED:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Task is already completed"
        )

    task.status = TaskStatus.COMPLETED
    task.completed_at = datetime.utcnow()
    task.progress_percentage = 100.0

    if actual_duration:
        task.actual_duration = actual_duration

    db.commit()

    return {"message": "Task completed successfully"}
