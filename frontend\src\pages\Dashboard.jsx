import React, { useState, useEffect } from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  Button,
  IconButton,
  Chip,
  LinearProgress,
  Avatar,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
  Divider,
} from '@mui/material';
import {
  Add as AddIcon,
  PlayArrow as PlayIcon,
  CheckCircle as CheckIcon,
  Schedule as ScheduleIcon,
  TrendingUp as TrendingUpIcon,
  Energy as EnergyIcon,
  Assignment as TaskIcon,
} from '@mui/icons-material';
import { motion } from 'framer-motion';
import { useQuery } from 'react-query';
import { format, isToday, isTomorrow } from 'date-fns';

import { tasksAPI, analyticsAPI } from '../services/api';
import { useAuth } from '../hooks/useAuth';
import LoadingSpinner from '../components/Common/LoadingSpinner';
import ErrorMessage from '../components/Common/ErrorMessage';

const Dashboard = () => {
  const { user } = useAuth();
  const [selectedPeriod, setSelectedPeriod] = useState('today');

  // Fetch dashboard data
  const { data: tasks, isLoading: tasksLoading } = useQuery(
    ['tasks', { status: 'pending', limit: 10 }],
    () => tasksAPI.getTasks({ status: 'pending', limit: 10 }),
    { select: (response) => response.data.tasks }
  );

  const { data: todayTasks, isLoading: todayTasksLoading } = useQuery(
    ['todayTasks'],
    () => tasksAPI.getTasks({ 
      scheduled_start: format(new Date(), 'yyyy-MM-dd'),
      limit: 5 
    }),
    { select: (response) => response.data.tasks }
  );

  const { data: productivity, isLoading: productivityLoading } = useQuery(
    ['productivity', selectedPeriod],
    () => analyticsAPI.getProductivityMetrics({ period: selectedPeriod }),
    { select: (response) => response.data }
  );

  const { data: insights } = useQuery(
    ['insights'],
    () => analyticsAPI.getInsights(),
    { select: (response) => response.data }
  );

  const isLoading = tasksLoading || todayTasksLoading || productivityLoading;

  if (isLoading) {
    return <LoadingSpinner />;
  }

  const getTaskPriorityColor = (priority) => {
    switch (priority) {
      case 'urgent': return 'error';
      case 'high': return 'warning';
      case 'medium': return 'info';
      case 'low': return 'success';
      default: return 'default';
    }
  };

  const getTimeOfDayGreeting = () => {
    const hour = new Date().getHours();
    if (hour < 12) return 'Good morning';
    if (hour < 17) return 'Good afternoon';
    return 'Good evening';
  };

  const formatTaskDate = (date) => {
    if (!date) return '';
    const taskDate = new Date(date);
    if (isToday(taskDate)) return 'Today';
    if (isTomorrow(taskDate)) return 'Tomorrow';
    return format(taskDate, 'MMM dd');
  };

  return (
    <Box sx={{ p: 3 }}>
      {/* Welcome Section */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <Box sx={{ mb: 4 }}>
          <Typography variant="h4" gutterBottom>
            {getTimeOfDayGreeting()}, {user?.full_name?.split(' ')[0] || 'there'}! 👋
          </Typography>
          <Typography variant="body1" color="text.secondary">
            Here's your productivity overview for today
          </Typography>
        </Box>
      </motion.div>

      <Grid container spacing={3}>
        {/* Quick Stats */}
        <Grid item xs={12} md={8}>
          <Grid container spacing={2}>
            <Grid item xs={6} sm={3}>
              <motion.div
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ delay: 0.1 }}
              >
                <Card>
                  <CardContent sx={{ textAlign: 'center' }}>
                    <TaskIcon color="primary" sx={{ fontSize: 40, mb: 1 }} />
                    <Typography variant="h4" color="primary">
                      {productivity?.tasks_completed || 0}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Tasks Completed
                    </Typography>
                  </CardContent>
                </Card>
              </motion.div>
            </Grid>

            <Grid item xs={6} sm={3}>
              <motion.div
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ delay: 0.2 }}
              >
                <Card>
                  <CardContent sx={{ textAlign: 'center' }}>
                    <TrendingUpIcon color="success" sx={{ fontSize: 40, mb: 1 }} />
                    <Typography variant="h4" color="success.main">
                      {productivity?.productivity_score?.toFixed(1) || '0.0'}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Productivity Score
                    </Typography>
                  </CardContent>
                </Card>
              </motion.div>
            </Grid>

            <Grid item xs={6} sm={3}>
              <motion.div
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ delay: 0.3 }}
              >
                <Card>
                  <CardContent sx={{ textAlign: 'center' }}>
                    <EnergyIcon color="warning" sx={{ fontSize: 40, mb: 1 }} />
                    <Typography variant="h4" color="warning.main">
                      {productivity?.energy_score?.toFixed(1) || '0.0'}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Energy Level
                    </Typography>
                  </CardContent>
                </Card>
              </motion.div>
            </Grid>

            <Grid item xs={6} sm={3}>
              <motion.div
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ delay: 0.4 }}
              >
                <Card>
                  <CardContent sx={{ textAlign: 'center' }}>
                    <ScheduleIcon color="info" sx={{ fontSize: 40, mb: 1 }} />
                    <Typography variant="h4" color="info.main">
                      {Math.round((productivity?.total_time_spent || 0) / 60)}h
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Time Focused
                    </Typography>
                  </CardContent>
                </Card>
              </motion.div>
            </Grid>
          </Grid>
        </Grid>

        {/* Quick Actions */}
        <Grid item xs={12} md={4}>
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.5 }}
          >
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Quick Actions
                </Typography>
                <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                  <Button
                    variant="contained"
                    startIcon={<AddIcon />}
                    fullWidth
                    onClick={() => {/* Navigate to add task */}}
                  >
                    Add New Task
                  </Button>
                  <Button
                    variant="outlined"
                    startIcon={<ScheduleIcon />}
                    fullWidth
                    onClick={() => {/* Trigger AI scheduling */}}
                  >
                    AI Schedule Tasks
                  </Button>
                  <Button
                    variant="outlined"
                    startIcon={<TrendingUpIcon />}
                    fullWidth
                    onClick={() => {/* Navigate to analytics */}}
                  >
                    View Analytics
                  </Button>
                </Box>
              </CardContent>
            </Card>
          </motion.div>
        </Grid>

        {/* Today's Schedule */}
        <Grid item xs={12} md={6}>
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.6 }}
          >
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Today's Schedule
                </Typography>
                {todayTasks && todayTasks.length > 0 ? (
                  <List>
                    {todayTasks.map((task, index) => (
                      <React.Fragment key={task.id}>
                        <ListItem
                          secondaryAction={
                            <IconButton
                              edge="end"
                              onClick={() => {/* Start task */}}
                            >
                              <PlayIcon />
                            </IconButton>
                          }
                        >
                          <ListItemAvatar>
                            <Avatar sx={{ bgcolor: 'primary.main' }}>
                              <TaskIcon />
                            </Avatar>
                          </ListItemAvatar>
                          <ListItemText
                            primary={task.title}
                            secondary={
                              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mt: 0.5 }}>
                                <Chip
                                  label={task.priority}
                                  size="small"
                                  color={getTaskPriorityColor(task.priority)}
                                />
                                {task.scheduled_start && (
                                  <Typography variant="caption" color="text.secondary">
                                    {format(new Date(task.scheduled_start), 'HH:mm')}
                                  </Typography>
                                )}
                              </Box>
                            }
                          />
                        </ListItem>
                        {index < todayTasks.length - 1 && <Divider />}
                      </React.Fragment>
                    ))}
                  </List>
                ) : (
                  <Typography color="text.secondary" sx={{ textAlign: 'center', py: 2 }}>
                    No tasks scheduled for today
                  </Typography>
                )}
              </CardContent>
            </Card>
          </motion.div>
        </Grid>

        {/* Recent Tasks */}
        <Grid item xs={12} md={6}>
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.7 }}
          >
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Recent Tasks
                </Typography>
                {tasks && tasks.length > 0 ? (
                  <List>
                    {tasks.slice(0, 5).map((task, index) => (
                      <React.Fragment key={task.id}>
                        <ListItem>
                          <ListItemAvatar>
                            <Avatar sx={{ bgcolor: 'secondary.main' }}>
                              <TaskIcon />
                            </Avatar>
                          </ListItemAvatar>
                          <ListItemText
                            primary={task.title}
                            secondary={
                              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mt: 0.5 }}>
                                <Chip
                                  label={task.status}
                                  size="small"
                                  variant="outlined"
                                />
                                <Typography variant="caption" color="text.secondary">
                                  {formatTaskDate(task.due_date)}
                                </Typography>
                              </Box>
                            }
                          />
                        </ListItem>
                        {index < Math.min(tasks.length, 5) - 1 && <Divider />}
                      </React.Fragment>
                    ))}
                  </List>
                ) : (
                  <Typography color="text.secondary" sx={{ textAlign: 'center', py: 2 }}>
                    No recent tasks
                  </Typography>
                )}
              </CardContent>
            </Card>
          </motion.div>
        </Grid>

        {/* AI Insights */}
        {insights && (
          <Grid item xs={12}>
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.8 }}
            >
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    AI Insights & Recommendations
                  </Typography>
                  <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                    {insights.recommendations?.map((recommendation, index) => (
                      <Box key={index} sx={{ p: 2, bgcolor: 'background.default', borderRadius: 1 }}>
                        <Typography variant="body1" gutterBottom>
                          {recommendation.title}
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          {recommendation.description}
                        </Typography>
                      </Box>
                    ))}
                  </Box>
                </CardContent>
              </Card>
            </motion.div>
          </Grid>
        )}
      </Grid>
    </Box>
  );
};

export default Dashboard;
