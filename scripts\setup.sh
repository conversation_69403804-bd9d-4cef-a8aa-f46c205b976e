#!/bin/bash

# FlowMind Setup Script
# This script sets up the FlowMind development environment

set -e

echo "🚀 Setting up FlowMind - Your AI Workflow Orchestrator"
echo "=================================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if required tools are installed
check_requirements() {
    print_status "Checking system requirements..."
    
    # Check Python
    if ! command -v python3 &> /dev/null; then
        print_error "Python 3.9+ is required but not installed"
        exit 1
    fi
    
    python_version=$(python3 --version | cut -d' ' -f2)
    print_success "Python $python_version found"
    
    # Check Node.js
    if ! command -v node &> /dev/null; then
        print_error "Node.js 18+ is required but not installed"
        exit 1
    fi
    
    node_version=$(node --version)
    print_success "Node.js $node_version found"
    
    # Check Docker (optional)
    if command -v docker &> /dev/null; then
        docker_version=$(docker --version | cut -d' ' -f3 | cut -d',' -f1)
        print_success "Docker $docker_version found"
    else
        print_warning "Docker not found - Docker setup will be skipped"
    fi
    
    # Check PostgreSQL
    if command -v psql &> /dev/null; then
        pg_version=$(psql --version | cut -d' ' -f3)
        print_success "PostgreSQL $pg_version found"
    else
        print_warning "PostgreSQL not found - please install PostgreSQL 13+"
    fi
    
    # Check Redis
    if command -v redis-cli &> /dev/null; then
        redis_version=$(redis-cli --version | cut -d' ' -f2)
        print_success "Redis $redis_version found"
    else
        print_warning "Redis not found - please install Redis 6+"
    fi
}

# Setup environment variables
setup_environment() {
    print_status "Setting up environment variables..."
    
    if [ ! -f .env ]; then
        cp .env.example .env
        print_success "Created .env file from template"
        print_warning "Please edit .env file with your configuration"
    else
        print_warning ".env file already exists - skipping"
    fi
}

# Setup Python backend
setup_backend() {
    print_status "Setting up Python backend..."
    
    cd backend
    
    # Create virtual environment
    if [ ! -d "venv" ]; then
        python3 -m venv venv
        print_success "Created Python virtual environment"
    fi
    
    # Activate virtual environment
    source venv/bin/activate
    
    # Upgrade pip
    pip install --upgrade pip
    
    # Install dependencies
    pip install -r requirements.txt
    print_success "Installed Python dependencies"
    
    # Setup database
    print_status "Setting up database..."
    
    # Check if database exists
    if command -v psql &> /dev/null; then
        # Create database if it doesn't exist
        createdb flowmind 2>/dev/null || print_warning "Database 'flowmind' may already exist"
        
        # Run migrations
        alembic upgrade head
        print_success "Database migrations completed"
    else
        print_warning "PostgreSQL not available - skipping database setup"
    fi
    
    cd ..
}

# Setup Node.js frontend
setup_frontend() {
    print_status "Setting up Node.js frontend..."
    
    cd frontend
    
    # Install dependencies
    npm install
    print_success "Installed Node.js dependencies"
    
    cd ..
}

# Setup Docker environment
setup_docker() {
    if command -v docker &> /dev/null; then
        print_status "Setting up Docker environment..."
        
        # Build Docker images
        docker-compose -f docker/docker-compose.yml build
        print_success "Built Docker images"
        
        print_status "Docker setup complete. Use 'docker-compose up' to start services"
    else
        print_warning "Docker not available - skipping Docker setup"
    fi
}

# Create necessary directories
create_directories() {
    print_status "Creating necessary directories..."
    
    mkdir -p logs
    mkdir -p uploads
    mkdir -p models
    mkdir -p cache
    
    print_success "Created application directories"
}

# Setup development tools
setup_dev_tools() {
    print_status "Setting up development tools..."
    
    # Setup pre-commit hooks
    if command -v pre-commit &> /dev/null; then
        pre-commit install
        print_success "Installed pre-commit hooks"
    else
        print_warning "pre-commit not found - install with 'pip install pre-commit'"
    fi
}

# Generate secret keys
generate_secrets() {
    print_status "Generating secret keys..."
    
    # Generate JWT secret
    jwt_secret=$(python3 -c "import secrets; print(secrets.token_urlsafe(32))")
    
    # Update .env file
    if [ -f .env ]; then
        sed -i.bak "s/your-super-secret-jwt-key-change-this-in-production/$jwt_secret/" .env
        print_success "Generated JWT secret key"
    fi
}

# Main setup function
main() {
    echo
    print_status "Starting FlowMind setup..."
    echo
    
    check_requirements
    echo
    
    setup_environment
    echo
    
    create_directories
    echo
    
    setup_backend
    echo
    
    setup_frontend
    echo
    
    generate_secrets
    echo
    
    setup_dev_tools
    echo
    
    setup_docker
    echo
    
    print_success "🎉 FlowMind setup completed successfully!"
    echo
    echo "Next steps:"
    echo "1. Edit .env file with your configuration"
    echo "2. Start the backend: cd backend && source venv/bin/activate && uvicorn app.main:app --reload"
    echo "3. Start the frontend: cd frontend && npm start"
    echo "4. Or use Docker: docker-compose -f docker/docker-compose.yml up"
    echo
    echo "Visit http://localhost:3000 to access FlowMind"
    echo
}

# Run main function
main "$@"
