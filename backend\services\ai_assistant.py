"""
AI Assistant Service for task enhancement and natural language processing.
"""

import json
import re
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
import openai
from app.config import settings
from models.task import TaskPriority, EnergyLevel


class AIAssistantService:
    """AI-powered assistant for task management and enhancement."""
    
    def __init__(self):
        if settings.openai_api_key:
            openai.api_key = settings.openai_api_key
        self.model = settings.openai_model
        self.max_tokens = settings.openai_max_tokens
    
    async def enhance_task_data(self, task_data: Dict[str, Any]) -> Dict[str, Any]:
        """Enhance task data using AI analysis."""
        
        if not settings.openai_api_key:
            return self._fallback_enhancement(task_data)
        
        try:
            prompt = self._create_enhancement_prompt(task_data)
            
            response = await openai.ChatCompletion.acreate(
                model=self.model,
                messages=[
                    {
                        "role": "system",
                        "content": "You are an AI assistant that helps enhance task data. "
                                 "Analyze the task and provide structured improvements."
                    },
                    {
                        "role": "user",
                        "content": prompt
                    }
                ],
                max_tokens=self.max_tokens,
                temperature=0.3
            )
            
            result = response.choices[0].message.content
            return self._parse_enhancement_result(result, task_data)
            
        except Exception as e:
            print(f"AI enhancement error: {e}")
            return self._fallback_enhancement(task_data)
    
    def _create_enhancement_prompt(self, task_data: Dict[str, Any]) -> str:
        """Create prompt for task enhancement."""
        
        title = task_data.get('title', '')
        description = task_data.get('description', '')
        
        prompt = f"""
        Analyze this task and provide enhancements:
        
        Title: {title}
        Description: {description}
        Current Priority: {task_data.get('priority', 'medium')}
        Current Energy Level: {task_data.get('energy_level_required', 'medium')}
        
        Please provide a JSON response with the following structure:
        {{
            "priority": "low|medium|high|urgent",
            "priority_confidence": 0.0-1.0,
            "energy_level_required": "low|medium|high",
            "estimated_duration": minutes_as_integer,
            "duration_estimate": minutes_as_integer,
            "category_suggestion": "category_name",
            "category_confidence": 0.0-1.0,
            "suggested_tags": ["tag1", "tag2", "tag3"],
            "complexity_score": 0.0-10.0,
            "focus_required": true|false,
            "reasoning": "explanation of analysis"
        }}
        
        Consider:
        - Task complexity and scope
        - Required mental energy and focus
        - Typical time requirements for similar tasks
        - Appropriate priority based on urgency and importance
        - Relevant tags and categorization
        """
        
        return prompt
    
    def _parse_enhancement_result(self, result: str, original_data: Dict[str, Any]) -> Dict[str, Any]:
        """Parse AI enhancement result."""
        
        try:
            # Extract JSON from response
            json_match = re.search(r'\{.*\}', result, re.DOTALL)
            if json_match:
                enhancement_data = json.loads(json_match.group())
                
                # Convert string enums to actual enum values
                if 'priority' in enhancement_data:
                    priority_map = {
                        'low': TaskPriority.LOW,
                        'medium': TaskPriority.MEDIUM,
                        'high': TaskPriority.HIGH,
                        'urgent': TaskPriority.URGENT
                    }
                    enhancement_data['priority'] = priority_map.get(
                        enhancement_data['priority'].lower(),
                        TaskPriority.MEDIUM
                    )
                
                if 'energy_level_required' in enhancement_data:
                    energy_map = {
                        'low': EnergyLevel.LOW,
                        'medium': EnergyLevel.MEDIUM,
                        'high': EnergyLevel.HIGH
                    }
                    enhancement_data['energy_level_required'] = energy_map.get(
                        enhancement_data['energy_level_required'].lower(),
                        EnergyLevel.MEDIUM
                    )
                
                return enhancement_data
                
        except (json.JSONDecodeError, KeyError) as e:
            print(f"Error parsing AI response: {e}")
        
        return self._fallback_enhancement(original_data)
    
    def _fallback_enhancement(self, task_data: Dict[str, Any]) -> Dict[str, Any]:
        """Fallback enhancement when AI is not available."""
        
        title = task_data.get('title', '').lower()
        description = task_data.get('description', '').lower()
        
        # Simple rule-based enhancement
        enhancement = {
            'priority_confidence': 0.5,
            'category_confidence': 0.3,
            'suggested_tags': [],
            'complexity_score': 5.0,
            'focus_required': True
        }
        
        # Estimate duration based on keywords
        if any(word in title + description for word in ['quick', 'brief', 'short']):
            enhancement['estimated_duration'] = 15
        elif any(word in title + description for word in ['meeting', 'call', 'review']):
            enhancement['estimated_duration'] = 30
        elif any(word in title + description for word in ['project', 'develop', 'create']):
            enhancement['estimated_duration'] = 120
        else:
            enhancement['estimated_duration'] = 60
        
        # Determine priority based on keywords
        if any(word in title + description for word in ['urgent', 'asap', 'critical', 'emergency']):
            enhancement['priority'] = TaskPriority.URGENT
            enhancement['priority_confidence'] = 0.8
        elif any(word in title + description for word in ['important', 'priority', 'deadline']):
            enhancement['priority'] = TaskPriority.HIGH
            enhancement['priority_confidence'] = 0.7
        else:
            enhancement['priority'] = task_data.get('priority', TaskPriority.MEDIUM)
        
        # Determine energy level
        if any(word in title + description for word in ['creative', 'design', 'plan', 'strategy']):
            enhancement['energy_level_required'] = EnergyLevel.HIGH
        elif any(word in title + description for word in ['admin', 'organize', 'file', 'sort']):
            enhancement['energy_level_required'] = EnergyLevel.LOW
        else:
            enhancement['energy_level_required'] = task_data.get('energy_level_required', EnergyLevel.MEDIUM)
        
        # Generate tags based on content
        tag_keywords = {
            'work': ['work', 'job', 'office', 'business', 'professional'],
            'personal': ['personal', 'home', 'family', 'self'],
            'health': ['health', 'exercise', 'doctor', 'fitness', 'wellness'],
            'learning': ['learn', 'study', 'course', 'education', 'training'],
            'creative': ['creative', 'design', 'art', 'write', 'create'],
            'admin': ['admin', 'paperwork', 'organize', 'file', 'document'],
            'communication': ['email', 'call', 'meeting', 'message', 'contact']
        }
        
        for tag, keywords in tag_keywords.items():
            if any(keyword in title + description for keyword in keywords):
                enhancement['suggested_tags'].append(tag)
        
        return enhancement
    
    async def process_natural_language_task(self, text: str) -> Dict[str, Any]:
        """Process natural language input to extract task information."""
        
        if not settings.openai_api_key:
            return self._fallback_nlp_processing(text)
        
        try:
            prompt = f"""
            Extract task information from this natural language input:
            "{text}"
            
            Provide a JSON response with:
            {{
                "title": "extracted task title",
                "description": "detailed description",
                "priority": "low|medium|high|urgent",
                "due_date": "ISO datetime or null",
                "estimated_duration": minutes_as_integer,
                "energy_level_required": "low|medium|high",
                "suggested_tags": ["tag1", "tag2"],
                "confidence": 0.0-1.0
            }}
            
            If multiple tasks are mentioned, focus on the primary one.
            """
            
            response = await openai.ChatCompletion.acreate(
                model=self.model,
                messages=[
                    {
                        "role": "system",
                        "content": "You are an AI assistant that extracts structured task information from natural language."
                    },
                    {
                        "role": "user",
                        "content": prompt
                    }
                ],
                max_tokens=self.max_tokens,
                temperature=0.2
            )
            
            result = response.choices[0].message.content
            return self._parse_nlp_result(result, text)
            
        except Exception as e:
            print(f"NLP processing error: {e}")
            return self._fallback_nlp_processing(text)
    
    def _parse_nlp_result(self, result: str, original_text: str) -> Dict[str, Any]:
        """Parse NLP processing result."""
        
        try:
            json_match = re.search(r'\{.*\}', result, re.DOTALL)
            if json_match:
                task_data = json.loads(json_match.group())
                
                # Convert due_date string to datetime if present
                if task_data.get('due_date') and task_data['due_date'] != 'null':
                    try:
                        task_data['due_date'] = datetime.fromisoformat(task_data['due_date'])
                    except ValueError:
                        task_data['due_date'] = None
                else:
                    task_data['due_date'] = None
                
                return task_data
                
        except (json.JSONDecodeError, KeyError) as e:
            print(f"Error parsing NLP response: {e}")
        
        return self._fallback_nlp_processing(original_text)
    
    def _fallback_nlp_processing(self, text: str) -> Dict[str, Any]:
        """Fallback NLP processing when AI is not available."""
        
        # Simple extraction using regex and keywords
        task_data = {
            'title': text[:100],  # First 100 characters as title
            'description': text if len(text) > 100 else None,
            'priority': TaskPriority.MEDIUM,
            'due_date': None,
            'estimated_duration': 60,
            'energy_level_required': EnergyLevel.MEDIUM,
            'suggested_tags': [],
            'confidence': 0.3
        }
        
        # Extract due date patterns
        date_patterns = [
            r'by (\w+day)',  # by Friday
            r'due (\w+day)',  # due Monday
            r'(\d{1,2}/\d{1,2})',  # 12/25
            r'(tomorrow|today)',
        ]
        
        for pattern in date_patterns:
            match = re.search(pattern, text.lower())
            if match:
                # Simple date parsing (would need more sophisticated logic)
                if 'tomorrow' in match.group(1):
                    task_data['due_date'] = datetime.now() + timedelta(days=1)
                elif 'today' in match.group(1):
                    task_data['due_date'] = datetime.now()
                break
        
        return task_data
