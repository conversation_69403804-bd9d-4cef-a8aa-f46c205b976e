"""
Configuration management for FlowMind application.
"""

import os
from typing import List, Optional
from pydantic import BaseSettings, validator
from functools import lru_cache


class Settings(BaseSettings):
    """Application settings loaded from environment variables."""
    
    # Application
    app_name: str = "FlowMind"
    app_version: str = "1.0.0"
    debug: bool = False
    environment: str = "development"
    log_level: str = "INFO"
    
    # Server
    host: str = "0.0.0.0"
    port: int = 8000
    allowed_hosts: List[str] = ["localhost", "127.0.0.1", "0.0.0.0"]
    cors_origins: List[str] = ["http://localhost:3000"]
    
    # Database
    database_url: str
    redis_url: str = "redis://localhost:6379/0"
    
    # JWT
    jwt_secret_key: str
    jwt_algorithm: str = "HS256"
    jwt_access_token_expire_minutes: int = 30
    jwt_refresh_token_expire_days: int = 7
    
    # External APIs
    openai_api_key: Optional[str] = None
    openai_model: str = "gpt-3.5-turbo"
    openai_max_tokens: int = 1000
    
    # Slack Integration
    slack_bot_token: Optional[str] = None
    slack_signing_secret: Optional[str] = None
    slack_client_id: Optional[str] = None
    slack_client_secret: Optional[str] = None
    
    # Google Integration
    google_client_id: Optional[str] = None
    google_client_secret: Optional[str] = None
    google_redirect_uri: str = "http://localhost:8000/integrations/google/callback"
    
    # Notion Integration
    notion_api_key: Optional[str] = None
    notion_client_id: Optional[str] = None
    notion_client_secret: Optional[str] = None
    
    # GitHub Integration
    github_token: Optional[str] = None
    github_client_id: Optional[str] = None
    github_client_secret: Optional[str] = None
    
    # Voice Processing
    speech_api_key: Optional[str] = None
    speech_region: Optional[str] = None
    
    # File Storage
    aws_access_key_id: Optional[str] = None
    aws_secret_access_key: Optional[str] = None
    aws_s3_bucket: Optional[str] = None
    aws_region: str = "us-east-1"
    
    # Security
    secure_cookies: bool = False
    https_only: bool = False
    
    # Email
    smtp_host: Optional[str] = None
    smtp_port: int = 587
    smtp_username: Optional[str] = None
    smtp_password: Optional[str] = None
    email_from: str = "<EMAIL>"
    
    # Monitoring
    sentry_dsn: Optional[str] = None
    analytics_enabled: bool = True
    
    # Rate Limiting
    rate_limit_requests_per_minute: int = 100
    rate_limit_burst: int = 20
    
    # Background Tasks
    celery_broker_url: str = "redis://localhost:6379/1"
    celery_result_backend: str = "redis://localhost:6379/2"
    
    # Machine Learning
    ml_model_path: str = "./models"
    energy_pattern_model: str = "energy_pattern_v1.pkl"
    nlp_model: str = "distilbert-base-uncased"
    voice_model: str = "wav2vec2-base-960h"
    
    # Feature Flags
    enable_voice_processing: bool = True
    enable_predictive_analytics: bool = True
    enable_integrations: bool = True
    enable_offline_mode: bool = True
    
    # Performance
    max_workers: int = 4
    worker_timeout: int = 300
    max_connections: int = 100
    connection_timeout: int = 30
    
    @validator("cors_origins", pre=True)
    def assemble_cors_origins(cls, v):
        if isinstance(v, str):
            return [i.strip() for i in v.split(",")]
        return v
    
    @validator("allowed_hosts", pre=True)
    def assemble_allowed_hosts(cls, v):
        if isinstance(v, str):
            return [i.strip() for i in v.split(",")]
        return v
    
    class Config:
        env_file = ".env"
        case_sensitive = False


@lru_cache()
def get_settings() -> Settings:
    """Get cached application settings."""
    return Settings()


# Global settings instance
settings = get_settings()
