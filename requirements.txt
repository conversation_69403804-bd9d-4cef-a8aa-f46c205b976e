# FastAPI and Web Framework
fastapi==0.104.1
uvicorn[standard]==0.24.0
python-multipart==0.0.6
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4

# Database
sqlalchemy==2.0.23
alembic==1.12.1
psycopg2-binary==2.9.9
redis==5.0.1

# AI and Machine Learning
openai==1.3.5
transformers==4.35.2
torch==2.1.1
scikit-learn==1.3.2
numpy==1.24.4
pandas==2.1.3
nltk==3.8.1
spacy==3.7.2

# Voice Processing
speechrecognition==3.10.0
pydub==0.25.1
librosa==0.10.1

# HTTP Clients and APIs
httpx==0.25.2
requests==2.31.0
aiohttp==3.9.1

# Task Queue and Background Jobs
celery==5.3.4
redis==5.0.1

# Validation and Serialization
pydantic==2.5.0
pydantic-settings==2.1.0

# Date and Time
python-dateutil==2.8.2
pytz==2023.3

# Logging and Monitoring
structlog==23.2.0
sentry-sdk[fastapi]==1.38.0

# Testing
pytest==7.4.3
pytest-asyncio==0.21.1
pytest-cov==4.1.0
httpx==0.25.2
faker==20.1.0

# Development Tools
black==23.11.0
isort==5.12.0
flake8==6.1.0
mypy==1.7.1

# Security
cryptography==41.0.8
bcrypt==4.1.1

# File Processing
python-magic==0.4.27
pillow==10.1.0

# Environment Management
python-dotenv==1.0.0

# Integration APIs
slack-sdk==3.26.1
google-api-python-client==2.108.0
google-auth-httplib2==0.1.1
google-auth-oauthlib==1.1.0
notion-client==2.2.1
PyGithub==2.1.1

# Utilities
click==8.1.7
rich==13.7.0
typer==0.9.0
