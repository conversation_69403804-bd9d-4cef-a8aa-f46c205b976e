{"name": "flowmind-frontend", "version": "1.0.0", "description": "FlowMind - Your AI Workflow Orchestrator Frontend", "private": true, "dependencies": {"@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^14.5.2", "react": "^18.2.0", "react-dom": "^18.2.0", "react-scripts": "5.0.1", "react-router-dom": "^6.8.1", "axios": "^1.6.2", "date-fns": "^2.30.0", "@mui/material": "^5.15.0", "@mui/icons-material": "^5.15.0", "@emotion/react": "^11.11.1", "@emotion/styled": "^11.11.0", "@mui/x-date-pickers": "^6.18.3", "@mui/x-charts": "^6.18.3", "recharts": "^2.8.0", "react-beautiful-dnd": "^13.1.1", "react-hook-form": "^7.48.2", "react-query": "^3.39.3", "socket.io-client": "^4.7.4", "react-speech-recognition": "^3.10.0", "web-speech-api": "^0.0.1", "react-hotkeys-hook": "^4.4.1", "react-toastify": "^9.1.3", "framer-motion": "^10.16.16", "react-intersection-observer": "^9.5.3", "react-virtualized": "^9.22.5", "lodash": "^4.17.21", "dayjs": "^1.11.10", "react-helmet-async": "^2.0.4", "react-error-boundary": "^4.0.11"}, "devDependencies": {"@types/react": "^18.2.45", "@types/react-dom": "^18.2.18", "@types/lodash": "^4.14.202", "@types/react-beautiful-dnd": "^13.1.8", "typescript": "^4.9.5", "eslint": "^8.56.0", "eslint-config-react-app": "^7.0.1", "prettier": "^3.1.1", "husky": "^8.0.3", "lint-staged": "^15.2.0", "@craco/craco": "^7.1.0", "tailwindcss": "^3.3.6", "autoprefixer": "^10.4.16", "postcss": "^8.4.32"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "lint": "eslint src --ext .js,.jsx,.ts,.tsx", "lint:fix": "eslint src --ext .js,.jsx,.ts,.tsx --fix", "format": "prettier --write src/**/*.{js,jsx,ts,tsx,json,css,md}", "type-check": "tsc --noEmit", "analyze": "npm run build && npx bundle-analyzer build/static/js/*.js"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"src/**/*.{js,jsx,ts,tsx}": ["eslint --fix", "prettier --write"], "src/**/*.{json,css,md}": ["prettier --write"]}, "proxy": "http://localhost:8000"}