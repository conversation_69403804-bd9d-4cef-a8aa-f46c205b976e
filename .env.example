# Database Configuration
DATABASE_URL=postgresql://flowmind_user:flowmind_password@localhost:5432/flowmind
REDIS_URL=redis://localhost:6379/0

# JWT Configuration
JWT_SECRET_KEY=your-super-secret-jwt-key-change-this-in-production
JWT_ALGORITHM=HS256
JWT_ACCESS_TOKEN_EXPIRE_MINUTES=30
JWT_REFRESH_TOKEN_EXPIRE_DAYS=7

# External API Keys
OPENAI_API_KEY=sk-your-openai-api-key-here
OPENAI_MODEL=gpt-3.5-turbo
OPENAI_MAX_TOKENS=1000

# Slack Integration
SLACK_BOT_TOKEN=xoxb-your-slack-bot-token
SLACK_SIGNING_SECRET=your-slack-signing-secret
SLACK_CLIENT_ID=your-slack-client-id
SLACK_CLIENT_SECRET=your-slack-client-secret

# Google Workspace Integration
GOOGLE_CLIENT_ID=your-google-client-id.apps.googleusercontent.com
GOOGLE_CLIENT_SECRET=your-google-client-secret
GOOGLE_REDIRECT_URI=http://localhost:8000/integrations/google/callback

# Notion Integration
NOTION_API_KEY=secret_your-notion-api-key
NOTION_CLIENT_ID=your-notion-client-id
NOTION_CLIENT_SECRET=your-notion-client-secret

# GitHub Integration
GITHUB_TOKEN=ghp_your-github-personal-access-token
GITHUB_CLIENT_ID=your-github-oauth-app-client-id
GITHUB_CLIENT_SECRET=your-github-oauth-app-client-secret

# Voice Processing
SPEECH_API_KEY=your-speech-recognition-api-key
SPEECH_REGION=your-speech-service-region

# File Storage (AWS S3)
AWS_ACCESS_KEY_ID=your-aws-access-key-id
AWS_SECRET_ACCESS_KEY=your-aws-secret-access-key
AWS_S3_BUCKET=flowmind-storage
AWS_REGION=us-east-1

# Application Settings
DEBUG=true
ENVIRONMENT=development
LOG_LEVEL=INFO
CORS_ORIGINS=http://localhost:3000,http://localhost:3001,http://127.0.0.1:3000

# Security Settings
ALLOWED_HOSTS=localhost,127.0.0.1,0.0.0.0
SECURE_COOKIES=false
HTTPS_ONLY=false

# Email Configuration (Optional)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your-app-password
EMAIL_FROM=<EMAIL>

# Monitoring and Analytics
SENTRY_DSN=https://<EMAIL>/project-id
ANALYTICS_ENABLED=true

# Rate Limiting
RATE_LIMIT_REQUESTS_PER_MINUTE=100
RATE_LIMIT_BURST=20

# Background Tasks
CELERY_BROKER_URL=redis://localhost:6379/1
CELERY_RESULT_BACKEND=redis://localhost:6379/2

# Machine Learning Models
ML_MODEL_PATH=./models
ENERGY_PATTERN_MODEL=energy_pattern_v1.pkl
NLP_MODEL=distilbert-base-uncased
VOICE_MODEL=wav2vec2-base-960h

# Feature Flags
ENABLE_VOICE_PROCESSING=true
ENABLE_PREDICTIVE_ANALYTICS=true
ENABLE_INTEGRATIONS=true
ENABLE_OFFLINE_MODE=true

# Performance Settings
MAX_WORKERS=4
WORKER_TIMEOUT=300
MAX_CONNECTIONS=100
CONNECTION_TIMEOUT=30
