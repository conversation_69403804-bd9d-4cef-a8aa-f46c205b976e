{"name": "flowmind", "version": "1.0.0", "description": "FlowMind - Your AI Workflow Orchestrator", "main": "index.js", "scripts": {"setup": "chmod +x scripts/setup.sh && ./scripts/setup.sh", "dev": "concurrently \"npm run dev:backend\" \"npm run dev:frontend\"", "dev:backend": "cd backend && source venv/bin/activate && uvicorn app.main:app --reload --host 0.0.0.0 --port 8000", "dev:frontend": "cd frontend && npm start", "build": "npm run build:frontend", "build:frontend": "cd frontend && npm run build", "test": "npm run test:backend && npm run test:frontend", "test:backend": "cd backend && source venv/bin/activate && pytest tests/ -v", "test:frontend": "cd frontend && npm test -- --coverage --watchAll=false", "lint": "npm run lint:backend && npm run lint:frontend", "lint:backend": "cd backend && source venv/bin/activate && flake8 . && black --check . && isort --check-only .", "lint:frontend": "cd frontend && npm run lint", "format": "npm run format:backend && npm run format:frontend", "format:backend": "cd backend && source venv/bin/activate && black . && isort .", "format:frontend": "cd frontend && npm run format", "docker:build": "docker-compose -f docker/docker-compose.yml build", "docker:up": "docker-compose -f docker/docker-compose.yml up", "docker:down": "docker-compose -f docker/docker-compose.yml down", "docker:logs": "docker-compose -f docker/docker-compose.yml logs -f", "migrate": "cd backend && source venv/bin/activate && alembic upgrade head", "migrate:create": "cd backend && source venv/bin/activate && alembic revision --autogenerate -m", "seed": "cd backend && source venv/bin/activate && python scripts/seed_data.py", "backup": "chmod +x scripts/backup.sh && ./scripts/backup.sh", "deploy": "chmod +x scripts/deploy.sh && ./scripts/deploy.sh", "clean": "rm -rf backend/venv frontend/node_modules frontend/build", "install:all": "cd backend && pip install -r requirements.txt && cd ../frontend && npm install"}, "repository": {"type": "git", "url": "git+https://github.com/HectorTa1989/flowmind.git"}, "keywords": ["ai", "productivity", "task-management", "workflow", "scheduling", "energy-patterns", "automation", "assistant"], "author": "FlowMind Team", "license": "MIT", "bugs": {"url": "https://github.com/HectorTa1989/flowmind/issues"}, "homepage": "https://github.com/HectorTa1989/flowmind#readme", "devDependencies": {"concurrently": "^8.2.2", "husky": "^8.0.3", "lint-staged": "^15.2.0"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0", "python": ">=3.9.0"}, "husky": {"hooks": {"pre-commit": "lint-staged", "pre-push": "npm run test"}}, "lint-staged": {"backend/**/*.py": ["black", "isort", "flake8"], "frontend/src/**/*.{js,jsx,ts,tsx}": ["eslint --fix", "prettier --write"], "*.{json,md,yml,yaml}": ["prettier --write"]}}