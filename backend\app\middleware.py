"""
Custom middleware for FlowMind application.
"""

import time
import logging
from typing import Callable
from fastapi import Request, Response, HTTPException
from fastapi.responses import JSONResponse
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.types import <PERSON>GI<PERSON>pp
import redis
from app.config import settings
from app.database import get_redis

logger = logging.getLogger(__name__)


class LoggingMiddleware(BaseHTTPMiddleware):
    """Middleware for request/response logging."""
    
    def __init__(self, app: ASGIApp):
        super().__init__(app)
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        start_time = time.time()
        
        # Log request
        logger.info(
            f"Request: {request.method} {request.url.path} "
            f"from {request.client.host if request.client else 'unknown'}"
        )
        
        # Process request
        response = await call_next(request)
        
        # Calculate processing time
        process_time = time.time() - start_time
        
        # Log response
        logger.info(
            f"Response: {response.status_code} "
            f"processed in {process_time:.4f}s"
        )
        
        # Add processing time header
        response.headers["X-Process-Time"] = str(process_time)
        
        return response


class RateLimitMiddleware(BaseHTTPMiddleware):
    """Middleware for rate limiting requests."""
    
    def __init__(self, app: ASGIApp):
        super().__init__(app)
        self.redis_client = None
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        # Skip rate limiting for health checks and static files
        if request.url.path in ["/health", "/", "/docs", "/redoc"]:
            return await call_next(request)
        
        # Get client IP
        client_ip = request.client.host if request.client else "unknown"
        
        # Create rate limit key
        rate_limit_key = f"rate_limit:{client_ip}"
        
        try:
            # Get Redis client
            if not self.redis_client:
                self.redis_client = get_redis()
            
            # Check current request count
            current_requests = self.redis_client.get(rate_limit_key)
            
            if current_requests is None:
                # First request from this IP
                self.redis_client.setex(
                    rate_limit_key, 
                    60,  # 1 minute window
                    1
                )
            else:
                current_requests = int(current_requests)
                
                if current_requests >= settings.rate_limit_requests_per_minute:
                    # Rate limit exceeded
                    return JSONResponse(
                        status_code=429,
                        content={
                            "error": {
                                "code": 429,
                                "message": "Rate limit exceeded. Please try again later.",
                                "type": "rate_limit_error"
                            }
                        }
                    )
                
                # Increment request count
                self.redis_client.incr(rate_limit_key)
        
        except Exception as e:
            # Log error but don't block request if Redis is unavailable
            logger.warning(f"Rate limiting error: {e}")
        
        return await call_next(request)


class SecurityHeadersMiddleware(BaseHTTPMiddleware):
    """Middleware for adding security headers."""
    
    def __init__(self, app: ASGIApp):
        super().__init__(app)
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        response = await call_next(request)
        
        # Add security headers
        response.headers["X-Content-Type-Options"] = "nosniff"
        response.headers["X-Frame-Options"] = "DENY"
        response.headers["X-XSS-Protection"] = "1; mode=block"
        response.headers["Referrer-Policy"] = "strict-origin-when-cross-origin"
        
        if settings.https_only:
            response.headers["Strict-Transport-Security"] = (
                "max-age=31536000; includeSubDomains"
            )
        
        # Content Security Policy
        csp = (
            "default-src 'self'; "
            "script-src 'self' 'unsafe-inline' 'unsafe-eval'; "
            "style-src 'self' 'unsafe-inline'; "
            "img-src 'self' data: https:; "
            "font-src 'self' data:; "
            "connect-src 'self' https:; "
            "frame-ancestors 'none';"
        )
        response.headers["Content-Security-Policy"] = csp
        
        return response


class AuthenticationMiddleware(BaseHTTPMiddleware):
    """Middleware for handling authentication."""
    
    def __init__(self, app: ASGIApp):
        super().__init__(app)
        # Paths that don't require authentication
        self.public_paths = {
            "/",
            "/health",
            "/docs",
            "/redoc",
            "/openapi.json",
            "/auth/login",
            "/auth/register",
            "/auth/refresh"
        }
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        # Check if path requires authentication
        if request.url.path in self.public_paths:
            return await call_next(request)
        
        # Check for Authorization header
        authorization = request.headers.get("Authorization")
        
        if not authorization or not authorization.startswith("Bearer "):
            return JSONResponse(
                status_code=401,
                content={
                    "error": {
                        "code": 401,
                        "message": "Authentication required",
                        "type": "authentication_error"
                    }
                }
            )
        
        # Extract token
        token = authorization.split(" ")[1]
        
        # Validate token (this would be implemented in auth utilities)
        # For now, we'll let the endpoint handle token validation
        
        return await call_next(request)
