"""
Task Scheduler Service for energy-based task scheduling.
"""

from datetime import datetime, timed<PERSON><PERSON>
from typing import List, Dict, Optional, Tuple
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_
import json

from app.database import SessionLocal
from models.task import Task, TaskStatus, TaskPriority, EnergyLevel
from models.user import User, UserProfile
from models.analytics import EnergyPattern, ProductivityMetric


class TaskSchedulerService:
    """Energy-based intelligent task scheduler."""
    
    def __init__(self):
        self.db = SessionLocal()
    
    async def schedule_task(self, task_id: str, user_id: str) -> Dict[str, any]:
        """Schedule a single task based on user's energy patterns."""
        
        try:
            # Get task and user data
            task = self.db.query(Task).filter(Task.id == task_id).first()
            if not task:
                return {"error": "Task not found"}
            
            user = self.db.query(User).filter(User.id == user_id).first()
            if not user:
                return {"error": "User not found"}
            
            # Get user's energy patterns
            energy_patterns = self._get_user_energy_patterns(user_id)
            
            # Get user's work preferences
            work_preferences = self._get_work_preferences(user_id)
            
            # Find optimal time slot
            optimal_slot = self._find_optimal_time_slot(
                task, energy_patterns, work_preferences
            )
            
            if optimal_slot:
                task.scheduled_start = optimal_slot['start']
                task.scheduled_end = optimal_slot['end']
                self.db.commit()
                
                return {
                    "scheduled": True,
                    "start_time": optimal_slot['start'],
                    "end_time": optimal_slot['end'],
                    "confidence": optimal_slot['confidence'],
                    "reasoning": optimal_slot['reasoning']
                }
            else:
                return {
                    "scheduled": False,
                    "message": "No optimal time slot found"
                }
                
        except Exception as e:
            print(f"Scheduling error: {e}")
            return {"error": str(e)}
        finally:
            self.db.close()
    
    async def batch_schedule_tasks(self, user_id: str, limit: int = 50) -> Dict[str, any]:
        """Schedule multiple tasks using smart batching."""
        
        try:
            # Get unscheduled tasks
            unscheduled_tasks = self.db.query(Task).filter(
                and_(
                    Task.user_id == user_id,
                    Task.status == TaskStatus.PENDING,
                    Task.scheduled_start.is_(None)
                )
            ).order_by(
                Task.priority.desc(),
                Task.due_date.asc()
            ).limit(limit).all()
            
            if not unscheduled_tasks:
                return {"message": "No unscheduled tasks found"}
            
            # Get user data
            energy_patterns = self._get_user_energy_patterns(user_id)
            work_preferences = self._get_work_preferences(user_id)
            
            # Group tasks by similarity for batching
            task_batches = self._create_task_batches(unscheduled_tasks)
            
            scheduled_count = 0
            scheduling_results = []
            
            for batch in task_batches:
                batch_result = self._schedule_task_batch(
                    batch, energy_patterns, work_preferences
                )
                scheduling_results.extend(batch_result)
                scheduled_count += len([r for r in batch_result if r['scheduled']])
            
            return {
                "total_tasks": len(unscheduled_tasks),
                "scheduled_count": scheduled_count,
                "results": scheduling_results
            }
            
        except Exception as e:
            print(f"Batch scheduling error: {e}")
            return {"error": str(e)}
        finally:
            self.db.close()
    
    def _get_user_energy_patterns(self, user_id: str) -> Dict[str, any]:
        """Get user's energy patterns from historical data."""
        
        # Get recent energy patterns (last 30 days)
        recent_patterns = self.db.query(EnergyPattern).filter(
            and_(
                EnergyPattern.user_id == user_id,
                EnergyPattern.pattern_date >= datetime.now() - timedelta(days=30)
            )
        ).all()
        
        if not recent_patterns:
            # Return default energy pattern
            return {
                'peak_hours': [9, 10, 14, 15],  # 9-10 AM, 2-3 PM
                'low_hours': [13, 16, 17],      # 1 PM, 4-5 PM
                'confidence': 0.3
            }
        
        # Analyze patterns to find consistent peak and low energy times
        hourly_energy = [0] * 24
        pattern_count = len(recent_patterns)
        
        for pattern in recent_patterns:
            if pattern.energy_levels:
                energy_levels = json.loads(pattern.energy_levels) if isinstance(pattern.energy_levels, str) else pattern.energy_levels
                for hour, energy in enumerate(energy_levels):
                    hourly_energy[hour] += energy
        
        # Average energy levels
        avg_energy = [energy / pattern_count for energy in hourly_energy]
        
        # Find peak and low hours
        peak_threshold = max(avg_energy) * 0.8
        low_threshold = max(avg_energy) * 0.4
        
        peak_hours = [hour for hour, energy in enumerate(avg_energy) if energy >= peak_threshold]
        low_hours = [hour for hour, energy in enumerate(avg_energy) if energy <= low_threshold]
        
        return {
            'peak_hours': peak_hours,
            'low_hours': low_hours,
            'hourly_average': avg_energy,
            'confidence': min(pattern_count / 30.0, 1.0)  # Higher confidence with more data
        }
    
    def _get_work_preferences(self, user_id: str) -> Dict[str, any]:
        """Get user's work preferences."""
        
        user_profile = self.db.query(UserProfile).filter(
            UserProfile.user_id == user_id
        ).first()
        
        if not user_profile:
            return {
                'work_hours_start': 9,
                'work_hours_end': 17,
                'work_days': [0, 1, 2, 3, 4],  # Monday to Friday
                'focus_session_duration': 25,
                'break_duration': 5
            }
        
        work_days_map = {
            'monday': 0, 'tuesday': 1, 'wednesday': 2, 'thursday': 3,
            'friday': 4, 'saturday': 5, 'sunday': 6
        }
        
        work_days = [work_days_map[day] for day in user_profile.work_days if day in work_days_map]
        
        return {
            'work_hours_start': user_profile.work_hours_start,
            'work_hours_end': user_profile.work_hours_end,
            'work_days': work_days,
            'focus_session_duration': user_profile.focus_session_duration,
            'break_duration': user_profile.break_duration
        }
    
    def _find_optimal_time_slot(
        self, 
        task: Task, 
        energy_patterns: Dict[str, any], 
        work_preferences: Dict[str, any]
    ) -> Optional[Dict[str, any]]:
        """Find the optimal time slot for a task."""
        
        # Determine required energy level
        energy_requirement = self._get_energy_requirement(task)
        
        # Get available time slots
        available_slots = self._get_available_time_slots(
            task.user_id, 
            work_preferences,
            task.estimated_duration or 60
        )
        
        best_slot = None
        best_score = 0
        
        for slot in available_slots:
            score = self._calculate_slot_score(
                slot, task, energy_patterns, energy_requirement
            )
            
            if score > best_score:
                best_score = score
                best_slot = slot
        
        if best_slot:
            return {
                'start': best_slot['start'],
                'end': best_slot['end'],
                'confidence': best_score,
                'reasoning': self._generate_scheduling_reasoning(
                    task, best_slot, energy_patterns, best_score
                )
            }
        
        return None
    
    def _get_energy_requirement(self, task: Task) -> int:
        """Get energy requirement score for a task (1-10)."""
        
        energy_map = {
            EnergyLevel.LOW: 3,
            EnergyLevel.MEDIUM: 6,
            EnergyLevel.HIGH: 9
        }
        
        base_energy = energy_map.get(task.energy_level_required, 6)
        
        # Adjust based on priority
        priority_adjustment = {
            TaskPriority.LOW: -1,
            TaskPriority.MEDIUM: 0,
            TaskPriority.HIGH: 1,
            TaskPriority.URGENT: 2
        }
        
        return min(10, max(1, base_energy + priority_adjustment.get(task.priority, 0)))
    
    def _get_available_time_slots(
        self, 
        user_id: str, 
        work_preferences: Dict[str, any], 
        duration_minutes: int
    ) -> List[Dict[str, any]]:
        """Get available time slots for scheduling."""
        
        slots = []
        
        # Look ahead for the next 7 days
        for days_ahead in range(7):
            date = datetime.now().date() + timedelta(days=days_ahead)
            
            # Check if it's a work day
            if date.weekday() not in work_preferences['work_days']:
                continue
            
            # Generate hourly slots within work hours
            start_hour = work_preferences['work_hours_start']
            end_hour = work_preferences['work_hours_end']
            
            for hour in range(start_hour, end_hour):
                slot_start = datetime.combine(date, datetime.min.time().replace(hour=hour))
                slot_end = slot_start + timedelta(minutes=duration_minutes)
                
                # Check if slot is available (no conflicting tasks)
                if self._is_slot_available(user_id, slot_start, slot_end):
                    slots.append({
                        'start': slot_start,
                        'end': slot_end,
                        'hour': hour,
                        'day_of_week': date.weekday()
                    })
        
        return slots
    
    def _is_slot_available(self, user_id: str, start_time: datetime, end_time: datetime) -> bool:
        """Check if a time slot is available (no conflicting tasks)."""
        
        conflicting_tasks = self.db.query(Task).filter(
            and_(
                Task.user_id == user_id,
                Task.scheduled_start.isnot(None),
                Task.scheduled_end.isnot(None),
                or_(
                    and_(Task.scheduled_start <= start_time, Task.scheduled_end > start_time),
                    and_(Task.scheduled_start < end_time, Task.scheduled_end >= end_time),
                    and_(Task.scheduled_start >= start_time, Task.scheduled_end <= end_time)
                )
            )
        ).count()
        
        return conflicting_tasks == 0
    
    def _calculate_slot_score(
        self, 
        slot: Dict[str, any], 
        task: Task, 
        energy_patterns: Dict[str, any], 
        energy_requirement: int
    ) -> float:
        """Calculate a score for how well a time slot matches a task."""
        
        score = 0.0
        
        # Energy alignment score (0-40 points)
        hour = slot['hour']
        if hour in energy_patterns['peak_hours']:
            energy_score = 40 * (energy_requirement / 10.0)
        elif hour in energy_patterns['low_hours']:
            energy_score = 40 * (1 - energy_requirement / 10.0)
        else:
            # Average energy time
            energy_score = 20
        
        score += energy_score
        
        # Priority urgency score (0-30 points)
        priority_scores = {
            TaskPriority.URGENT: 30,
            TaskPriority.HIGH: 20,
            TaskPriority.MEDIUM: 10,
            TaskPriority.LOW: 5
        }
        score += priority_scores.get(task.priority, 10)
        
        # Due date proximity score (0-20 points)
        if task.due_date:
            days_until_due = (task.due_date.date() - slot['start'].date()).days
            if days_until_due <= 0:
                score += 20  # Overdue or due today
            elif days_until_due <= 1:
                score += 15  # Due tomorrow
            elif days_until_due <= 3:
                score += 10  # Due within 3 days
            else:
                score += 5   # Due later
        else:
            score += 5  # No due date
        
        # Time of day preference (0-10 points)
        if 9 <= hour <= 11:  # Morning focus time
            score += 10
        elif 14 <= hour <= 16:  # Afternoon focus time
            score += 8
        else:
            score += 5
        
        return min(100.0, score)  # Cap at 100
    
    def _generate_scheduling_reasoning(
        self, 
        task: Task, 
        slot: Dict[str, any], 
        energy_patterns: Dict[str, any], 
        score: float
    ) -> str:
        """Generate human-readable reasoning for scheduling decision."""
        
        hour = slot['hour']
        reasons = []
        
        if hour in energy_patterns['peak_hours']:
            reasons.append(f"Scheduled during your peak energy time ({hour}:00)")
        elif hour in energy_patterns['low_hours']:
            reasons.append(f"Scheduled during low energy time suitable for this task type")
        
        if task.priority == TaskPriority.URGENT:
            reasons.append("High priority task scheduled as soon as possible")
        
        if task.due_date:
            days_until_due = (task.due_date.date() - slot['start'].date()).days
            if days_until_due <= 1:
                reasons.append("Scheduled close to due date")
        
        if not reasons:
            reasons.append("Scheduled based on availability and task requirements")
        
        return ". ".join(reasons) + f" (Confidence: {score:.0f}%)"
    
    def _create_task_batches(self, tasks: List[Task]) -> List[List[Task]]:
        """Group similar tasks for batch scheduling."""
        
        # Simple batching by category and energy level
        batches = {}
        
        for task in tasks:
            key = f"{task.category_id}_{task.energy_level_required}"
            if key not in batches:
                batches[key] = []
            batches[key].append(task)
        
        return list(batches.values())
    
    def _schedule_task_batch(
        self, 
        batch: List[Task], 
        energy_patterns: Dict[str, any], 
        work_preferences: Dict[str, any]
    ) -> List[Dict[str, any]]:
        """Schedule a batch of similar tasks."""
        
        results = []
        
        for task in batch:
            result = self._find_optimal_time_slot(task, energy_patterns, work_preferences)
            
            if result:
                task.scheduled_start = result['start']
                task.scheduled_end = result['end']
                self.db.commit()
                
                results.append({
                    'task_id': str(task.id),
                    'scheduled': True,
                    'start_time': result['start'],
                    'end_time': result['end'],
                    'confidence': result['confidence']
                })
            else:
                results.append({
                    'task_id': str(task.id),
                    'scheduled': False,
                    'message': 'No suitable time slot found'
                })
        
        return results
