# FlowMind - Your AI Workflow Orchestrator

[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)
[![Python 3.9+](https://img.shields.io/badge/python-3.9+-blue.svg)](https://www.python.org/downloads/)
[![Node.js 18+](https://img.shields.io/badge/node.js-18+-green.svg)](https://nodejs.org/)
[![Docker](https://img.shields.io/badge/docker-ready-blue.svg)](https://www.docker.com/)

> **FlowMind** is an intelligent AI-powered productivity platform that orchestrates your workflow by understanding your energy patterns, managing tasks contextually, and integrating seamlessly with your favorite productivity tools.

## 🚀 Core Features

### 🧠 Contextual AI Assistant
- **Natural Language Processing**: Communicate with your tasks using everyday language
- **Smart Task Interpretation**: Automatically categorize and prioritize tasks based on context
- **Intelligent Suggestions**: Proactive recommendations for task optimization

### ⚡ Energy-Based Task Scheduling
- **Circadian Rhythm Analysis**: Learn your natural energy patterns
- **Optimal Task Timing**: Schedule demanding tasks during peak energy periods
- **Adaptive Scheduling**: Continuously adjust based on performance data

### 🔗 Cross-Platform Integrations
- **Slack**: Seamless team communication and task updates
- **Google Workspace**: Calendar, Gmail, and Drive integration
- **Notion**: Sync with your knowledge base and project management
- **GitHub**: Track development tasks and code review workflows
- **Microsoft 365**: Outlook, Teams, and OneDrive support

### 🎯 Smart Task Batching
- **Context Switching Minimization**: Group similar tasks together
- **Focus Time Optimization**: Create distraction-free work blocks
- **Intelligent Prioritization**: Dynamic priority adjustment based on deadlines and importance

### 📊 Predictive Analytics
- **Project Completion Forecasting**: AI-powered timeline predictions
- **Productivity Insights**: Detailed analytics on work patterns
- **Performance Optimization**: Identify bottlenecks and improvement opportunities

### 🎤 Voice-to-Task Conversion
- **Speech Recognition**: Convert voice notes to actionable tasks
- **Multi-language Support**: Process tasks in multiple languages
- **Hands-free Operation**: Perfect for mobile and accessibility needs

### 🔄 Real-time Synchronization
- **Cross-device Sync**: Access your workflow from anywhere
- **Offline Functionality**: Continue working without internet connection
- **Conflict Resolution**: Smart merging of offline changes

## 🏗️ System Architecture

```mermaid
graph TB
    subgraph "Frontend Layer"
        WEB[Web Dashboard]
        MOBILE[Mobile App]
        VOICE[Voice Interface]
    end
    
    subgraph "API Gateway"
        GATEWAY[FastAPI Gateway]
        AUTH[Authentication]
        RATE[Rate Limiting]
    end
    
    subgraph "Core Services"
        AI[AI Assistant Service]
        SCHEDULER[Task Scheduler]
        ANALYTICS[Analytics Engine]
        INTEGRATIONS[Integration Hub]
    end
    
    subgraph "AI/ML Layer"
        NLP[NLP Engine]
        ENERGY[Energy Pattern ML]
        PREDICT[Predictive Models]
        VOICE_AI[Voice Processing]
    end
    
    subgraph "Data Layer"
        POSTGRES[(PostgreSQL)]
        REDIS[(Redis Cache)]
        VECTOR[(Vector DB)]
        FILES[(File Storage)]
    end
    
    subgraph "External APIs"
        SLACK_API[Slack API]
        GOOGLE_API[Google APIs]
        NOTION_API[Notion API]
        GITHUB_API[GitHub API]
    end
    
    WEB --> GATEWAY
    MOBILE --> GATEWAY
    VOICE --> GATEWAY
    
    GATEWAY --> AI
    GATEWAY --> SCHEDULER
    GATEWAY --> ANALYTICS
    GATEWAY --> INTEGRATIONS
    
    AI --> NLP
    SCHEDULER --> ENERGY
    ANALYTICS --> PREDICT
    VOICE --> VOICE_AI
    
    AI --> POSTGRES
    SCHEDULER --> REDIS
    ANALYTICS --> VECTOR
    INTEGRATIONS --> FILES
    
    INTEGRATIONS --> SLACK_API
    INTEGRATIONS --> GOOGLE_API
    INTEGRATIONS --> NOTION_API
    INTEGRATIONS --> GITHUB_API
```

## 👤 User Workflow

```mermaid
flowchart TD
    START([User Opens FlowMind]) --> LOGIN{Authenticated?}
    LOGIN -->|No| AUTH[Login/Register]
    LOGIN -->|Yes| DASHBOARD[Dashboard]
    AUTH --> ONBOARD[Onboarding Flow]
    
    ONBOARD --> CONNECT[Connect Integrations]
    CONNECT --> ENERGY_SETUP[Energy Pattern Setup]
    ENERGY_SETUP --> DASHBOARD
    
    DASHBOARD --> TASK_INPUT{How to add task?}
    TASK_INPUT -->|Voice| VOICE_PROCESS[Voice Recognition]
    TASK_INPUT -->|Text| TEXT_INPUT[Text Input]
    TASK_INPUT -->|Integration| AUTO_IMPORT[Auto Import]
    
    VOICE_PROCESS --> AI_PROCESS[AI Processing]
    TEXT_INPUT --> AI_PROCESS
    AUTO_IMPORT --> AI_PROCESS
    
    AI_PROCESS --> CATEGORIZE[Categorize & Prioritize]
    CATEGORIZE --> SCHEDULE[Smart Scheduling]
    SCHEDULE --> BATCH[Task Batching]
    BATCH --> NOTIFY[Notifications]
    
    NOTIFY --> WORK[Work on Tasks]
    WORK --> TRACK[Progress Tracking]
    TRACK --> LEARN[Pattern Learning]
    LEARN --> OPTIMIZE[Optimization]
    OPTIMIZE --> DASHBOARD
    
    DASHBOARD --> ANALYTICS_VIEW[View Analytics]
    ANALYTICS_VIEW --> INSIGHTS[Productivity Insights]
    INSIGHTS --> DASHBOARD
```

## 📁 Project Structure

```
flowmind/
├── backend/
│   ├── app/
│   │   ├── __init__.py
│   │   ├── main.py                 # FastAPI application entry point
│   │   ├── config.py               # Configuration management
│   │   ├── database.py             # Database connection and models
│   │   └── middleware.py           # Custom middleware
│   ├── services/
│   │   ├── __init__.py
│   │   ├── ai_assistant.py         # AI assistant core logic
│   │   ├── task_scheduler.py       # Energy-based scheduling
│   │   ├── analytics.py            # Analytics and insights
│   │   ├── integrations/           # External service integrations
│   │   │   ├── __init__.py
│   │   │   ├── slack.py
│   │   │   ├── google_workspace.py
│   │   │   ├── notion.py
│   │   │   └── github.py
│   │   └── ml/                     # Machine learning models
│   │       ├── __init__.py
│   │       ├── nlp_engine.py
│   │       ├── energy_patterns.py
│   │       ├── predictive_models.py
│   │       └── voice_processing.py
│   ├── api/
│   │   ├── __init__.py
│   │   ├── auth.py                 # Authentication endpoints
│   │   ├── tasks.py                # Task management endpoints
│   │   ├── analytics.py            # Analytics endpoints
│   │   ├── integrations.py         # Integration endpoints
│   │   └── voice.py                # Voice processing endpoints
│   ├── models/
│   │   ├── __init__.py
│   │   ├── user.py                 # User data models
│   │   ├── task.py                 # Task data models
│   │   ├── integration.py          # Integration models
│   │   └── analytics.py            # Analytics models
│   ├── utils/
│   │   ├── __init__.py
│   │   ├── auth.py                 # Authentication utilities
│   │   ├── encryption.py           # Data encryption
│   │   ├── validators.py           # Input validation
│   │   └── helpers.py              # General utilities
│   └── tests/
│       ├── __init__.py
│       ├── test_ai_assistant.py
│       ├── test_scheduler.py
│       ├── test_integrations.py
│       └── test_analytics.py
├── frontend/
│   ├── public/
│   │   ├── index.html
│   │   ├── manifest.json
│   │   └── favicon.ico
│   ├── src/
│   │   ├── components/             # Reusable UI components
│   │   │   ├── Dashboard/
│   │   │   ├── TaskManager/
│   │   │   ├── Analytics/
│   │   │   ├── Settings/
│   │   │   └── Common/
│   │   ├── pages/                  # Page components
│   │   │   ├── Home.jsx
│   │   │   ├── Dashboard.jsx
│   │   │   ├── Tasks.jsx
│   │   │   ├── Analytics.jsx
│   │   │   └── Settings.jsx
│   │   ├── services/               # API service layer
│   │   │   ├── api.js
│   │   │   ├── auth.js
│   │   │   ├── tasks.js
│   │   │   └── analytics.js
│   │   ├── utils/                  # Frontend utilities
│   │   │   ├── constants.js
│   │   │   ├── helpers.js
│   │   │   └── validators.js
│   │   ├── hooks/                  # Custom React hooks
│   │   │   ├── useAuth.js
│   │   │   ├── useTasks.js
│   │   │   └── useAnalytics.js
│   │   ├── context/                # React context providers
│   │   │   ├── AuthContext.js
│   │   │   ├── TaskContext.js
│   │   │   └── ThemeContext.js
│   │   ├── styles/                 # CSS and styling
│   │   │   ├── globals.css
│   │   │   ├── components.css
│   │   │   └── themes.css
│   │   ├── App.jsx                 # Main App component
│   │   └── index.js                # React entry point
│   ├── package.json
│   └── package-lock.json
├── mobile/                         # React Native mobile app
│   ├── src/
│   │   ├── components/
│   │   ├── screens/
│   │   ├── services/
│   │   └── utils/
│   ├── package.json
│   └── app.json
├── docs/                           # Documentation
│   ├── api/                        # API documentation
│   ├── user-guide/                 # User guides
│   ├── developer/                  # Developer documentation
│   └── deployment/                 # Deployment guides
├── scripts/                        # Utility scripts
│   ├── setup.sh                    # Initial setup script
│   ├── deploy.sh                   # Deployment script
│   └── backup.sh                   # Backup script
├── docker/                         # Docker configuration
│   ├── Dockerfile.backend
│   ├── Dockerfile.frontend
│   └── docker-compose.yml
├── .env.example                    # Environment variables template
├── .gitignore                      # Git ignore rules
├── requirements.txt                # Python dependencies
├── package.json                    # Node.js dependencies
└── README.md                       # This file
```

## 🛠️ Installation & Setup

### Prerequisites
- Python 3.9 or higher
- Node.js 18 or higher
- PostgreSQL 13+
- Redis 6+
- Docker (optional)

### Quick Start with Docker

```bash
# Clone the repository
git clone https://github.com/HectorTa1989/flowmind.git
cd flowmind

# Copy environment variables
cp .env.example .env

# Edit .env with your configuration
nano .env

# Start all services with Docker Compose
docker-compose up -d

# Access the application
# Frontend: http://localhost:3000
# Backend API: http://localhost:8000
# API Documentation: http://localhost:8000/docs
```

### Manual Installation

#### Backend Setup

```bash
# Navigate to backend directory
cd backend

# Create virtual environment
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate

# Install dependencies
pip install -r requirements.txt

# Set up database
python -m alembic upgrade head

# Start the backend server
uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
```

#### Frontend Setup

```bash
# Navigate to frontend directory
cd frontend

# Install dependencies
npm install

# Start development server
npm start

# Build for production
npm run build
```

### Environment Variables

Create a `.env` file in the root directory:

```env
# Database Configuration
DATABASE_URL=postgresql://username:password@localhost:5432/flowmind
REDIS_URL=redis://localhost:6379

# JWT Configuration
JWT_SECRET_KEY=your-super-secret-jwt-key
JWT_ALGORITHM=HS256
JWT_ACCESS_TOKEN_EXPIRE_MINUTES=30

# External API Keys
OPENAI_API_KEY=your-openai-api-key
SLACK_BOT_TOKEN=xoxb-your-slack-bot-token
SLACK_SIGNING_SECRET=your-slack-signing-secret
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret
NOTION_API_KEY=your-notion-api-key
GITHUB_TOKEN=your-github-token

# Voice Processing
SPEECH_API_KEY=your-speech-api-key

# File Storage
AWS_ACCESS_KEY_ID=your-aws-access-key
AWS_SECRET_ACCESS_KEY=your-aws-secret-key
AWS_S3_BUCKET=your-s3-bucket-name

# Application Settings
DEBUG=true
CORS_ORIGINS=http://localhost:3000,http://localhost:3001
```

## 📚 API Documentation

### Authentication

#### POST /auth/register
Register a new user account.

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "securepassword",
  "full_name": "John Doe",
  "timezone": "America/New_York"
}
```

**Response:**
```json
{
  "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
  "token_type": "bearer",
  "user": {
    "id": "uuid",
    "email": "<EMAIL>",
    "full_name": "John Doe",
    "created_at": "2024-01-01T00:00:00Z"
  }
}
```

#### POST /auth/login
Authenticate user and get access token.

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "securepassword"
}
```

### Tasks

#### GET /tasks
Retrieve user's tasks with filtering and pagination.

**Query Parameters:**
- `status`: Filter by task status (pending, in_progress, completed)
- `priority`: Filter by priority (low, medium, high, urgent)
- `limit`: Number of tasks to return (default: 50)
- `offset`: Pagination offset (default: 0)

**Response:**
```json
{
  "tasks": [
    {
      "id": "uuid",
      "title": "Complete project proposal",
      "description": "Draft and review the Q1 project proposal",
      "status": "in_progress",
      "priority": "high",
      "due_date": "2024-01-15T17:00:00Z",
      "estimated_duration": 120,
      "energy_level_required": "high",
      "tags": ["work", "proposal", "urgent"],
      "created_at": "2024-01-01T09:00:00Z",
      "updated_at": "2024-01-01T14:30:00Z"
    }
  ],
  "total": 25,
  "limit": 50,
  "offset": 0
}
```

#### POST /tasks
Create a new task.

**Request Body:**
```json
{
  "title": "Complete project proposal",
  "description": "Draft and review the Q1 project proposal",
  "priority": "high",
  "due_date": "2024-01-15T17:00:00Z",
  "estimated_duration": 120,
  "tags": ["work", "proposal"]
}
```

#### PUT /tasks/{task_id}
Update an existing task.

#### DELETE /tasks/{task_id}
Delete a task.

### Voice Processing

#### POST /voice/transcribe
Convert voice input to text and create tasks.

**Request:**
- Multipart form data with audio file
- Supported formats: WAV, MP3, M4A

**Response:**
```json
{
  "transcription": "Create a task to review the marketing budget by Friday",
  "tasks_created": [
    {
      "id": "uuid",
      "title": "Review marketing budget",
      "due_date": "2024-01-12T17:00:00Z",
      "priority": "medium"
    }
  ]
}
```

### Analytics

#### GET /analytics/productivity
Get productivity insights and metrics.

**Query Parameters:**
- `period`: Time period (day, week, month, year)
- `start_date`: Start date for analysis
- `end_date`: End date for analysis

**Response:**
```json
{
  "period": "week",
  "tasks_completed": 23,
  "tasks_created": 28,
  "completion_rate": 0.82,
  "average_completion_time": 145,
  "energy_patterns": {
    "peak_hours": ["09:00", "14:00"],
    "low_hours": ["13:00", "16:00"]
  },
  "productivity_score": 8.5
}
```

### Integrations

#### GET /integrations
List available integrations and their status.

#### POST /integrations/{service}/connect
Connect to an external service.

#### DELETE /integrations/{service}/disconnect
Disconnect from an external service.

## 🧪 Testing

### Running Tests

```bash
# Backend tests
cd backend
pytest tests/ -v --cov=app

# Frontend tests
cd frontend
npm test

# Integration tests
npm run test:integration

# End-to-end tests
npm run test:e2e
```

### Test Coverage

The project maintains >90% test coverage across all core modules:
- AI Assistant Service
- Task Scheduling Engine
- Integration Services
- Analytics Engine
- Authentication System

## 🚀 Deployment

### Production Deployment

```bash
# Build production images
docker build -f docker/Dockerfile.backend -t flowmind-backend .
docker build -f docker/Dockerfile.frontend -t flowmind-frontend .

# Deploy with Docker Compose
docker-compose -f docker-compose.prod.yml up -d

# Or deploy to cloud platforms
# AWS ECS, Google Cloud Run, Azure Container Instances
```

### Environment-Specific Configurations

- **Development**: Full debugging, hot reload, test data
- **Staging**: Production-like environment for testing
- **Production**: Optimized performance, security hardened

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

### Development Guidelines

- Follow PEP 8 for Python code
- Use ESLint and Prettier for JavaScript/React code
- Write comprehensive tests for new features
- Update documentation for API changes
- Ensure all tests pass before submitting PR

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- OpenAI for GPT models and API
- The open-source community for amazing tools and libraries
- Contributors and beta testers

## 📞 Support

- **Documentation**: [docs.flowmind.ai](https://docs.flowmind.ai)
- **Issues**: [GitHub Issues](https://github.com/HectorTa1989/flowmind/issues)
- **Discussions**: [GitHub Discussions](https://github.com/HectorTa1989/flowmind/discussions)
- **Email**: <EMAIL>

---

**FlowMind** - Orchestrating your productivity with AI intelligence. 🚀
