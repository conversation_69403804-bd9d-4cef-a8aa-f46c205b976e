"""
Task-related database models.
"""

from sqlalchemy import (
    Column, String, <PERSON>olean, DateTime, Text, Integer, 
    ForeignKey, JSON, Float, Enum, Table
)
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
import uuid
import enum
from app.database import Base


class TaskStatus(enum.Enum):
    """Task status enumeration."""
    PENDING = "pending"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    CANCELLED = "cancelled"
    ON_HOLD = "on_hold"


class TaskPriority(enum.Enum):
    """Task priority enumeration."""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    URGENT = "urgent"


class EnergyLevel(enum.Enum):
    """Energy level required for task."""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"


# Association table for task tags (many-to-many)
task_tags = Table(
    'task_tags',
    Base.metadata,
    Column('task_id', UUID(as_uuid=True), ForeignKey('tasks.id'), primary_key=True),
    Column('tag_id', UUID(as_uuid=True), ForeignKey('tags.id'), primary_key=True)
)


class Task(Base):
    """Main task model."""
    
    __tablename__ = "tasks"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    user_id = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=False)
    category_id = Column(UUID(as_uuid=True), ForeignKey("task_categories.id"))
    
    # Basic task information
    title = Column(String(500), nullable=False)
    description = Column(Text)
    status = Column(Enum(TaskStatus), default=TaskStatus.PENDING)
    priority = Column(Enum(TaskPriority), default=TaskPriority.MEDIUM)
    
    # Scheduling information
    due_date = Column(DateTime(timezone=True))
    scheduled_start = Column(DateTime(timezone=True))
    scheduled_end = Column(DateTime(timezone=True))
    estimated_duration = Column(Integer)  # minutes
    actual_duration = Column(Integer)     # minutes
    
    # Energy and focus requirements
    energy_level_required = Column(Enum(EnergyLevel), default=EnergyLevel.MEDIUM)
    focus_required = Column(Boolean, default=True)
    
    # Progress tracking
    progress_percentage = Column(Float, default=0.0)
    time_spent = Column(Integer, default=0)  # minutes
    
    # AI-generated metadata
    ai_category_confidence = Column(Float)
    ai_priority_confidence = Column(Float)
    ai_duration_estimate = Column(Integer)
    ai_tags = Column(JSON)
    
    # External integration data
    external_id = Column(String(255))  # ID from external service
    external_source = Column(String(50))  # slack, notion, github, etc.
    external_url = Column(String(500))
    external_metadata = Column(JSON)
    
    # Completion tracking
    completed_at = Column(DateTime(timezone=True))
    cancelled_at = Column(DateTime(timezone=True))
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    user = relationship("User", back_populates="tasks")
    category = relationship("TaskCategory", back_populates="tasks")
    tags = relationship("TaskTag", secondary=task_tags, back_populates="tasks")
    dependencies = relationship(
        "TaskDependency", 
        foreign_keys="TaskDependency.task_id",
        back_populates="task"
    )
    dependent_tasks = relationship(
        "TaskDependency",
        foreign_keys="TaskDependency.depends_on_task_id",
        back_populates="depends_on_task"
    )
    completions = relationship("TaskCompletion", back_populates="task")
    
    def __repr__(self):
        return f"<Task(id={self.id}, title={self.title}, status={self.status})>"


class TaskCategory(Base):
    """Task category model."""
    
    __tablename__ = "task_categories"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    user_id = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=False)
    
    name = Column(String(100), nullable=False)
    description = Column(Text)
    color = Column(String(7))  # Hex color code
    icon = Column(String(50))  # Icon identifier
    
    # Category settings
    default_priority = Column(Enum(TaskPriority), default=TaskPriority.MEDIUM)
    default_energy_level = Column(Enum(EnergyLevel), default=EnergyLevel.MEDIUM)
    default_duration = Column(Integer)  # minutes
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    user = relationship("User")
    tasks = relationship("Task", back_populates="category")
    
    def __repr__(self):
        return f"<TaskCategory(id={self.id}, name={self.name})>"


class TaskTag(Base):
    """Task tag model."""
    
    __tablename__ = "tags"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    user_id = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=False)
    
    name = Column(String(50), nullable=False)
    color = Column(String(7))  # Hex color code
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # Relationships
    user = relationship("User")
    tasks = relationship("Task", secondary=task_tags, back_populates="tags")
    
    def __repr__(self):
        return f"<TaskTag(id={self.id}, name={self.name})>"


class TaskDependency(Base):
    """Task dependency model (task A depends on task B)."""
    
    __tablename__ = "task_dependencies"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    task_id = Column(UUID(as_uuid=True), ForeignKey("tasks.id"), nullable=False)
    depends_on_task_id = Column(UUID(as_uuid=True), ForeignKey("tasks.id"), nullable=False)
    
    # Dependency type
    dependency_type = Column(String(20), default="finish_to_start")  # finish_to_start, start_to_start, etc.
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # Relationships
    task = relationship("Task", foreign_keys=[task_id], back_populates="dependencies")
    depends_on_task = relationship("Task", foreign_keys=[depends_on_task_id], back_populates="dependent_tasks")
    
    def __repr__(self):
        return f"<TaskDependency(task_id={self.task_id}, depends_on={self.depends_on_task_id})>"
