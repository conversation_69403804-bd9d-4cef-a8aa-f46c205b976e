import axios from 'axios';
import { toast } from 'react-toastify';

// Create axios instance
const api = axios.create({
  baseURL: process.env.REACT_APP_API_URL || 'http://localhost:8000',
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor
api.interceptors.request.use(
  (config) => {
    // Add auth token if available
    const token = localStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }

    // Add request timestamp for debugging
    config.metadata = { startTime: new Date() };

    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor
api.interceptors.response.use(
  (response) => {
    // Calculate request duration
    const duration = new Date() - response.config.metadata.startTime;
    console.log(`API Request: ${response.config.method?.toUpperCase()} ${response.config.url} - ${duration}ms`);

    return response;
  },
  (error) => {
    // Calculate request duration
    if (error.config?.metadata) {
      const duration = new Date() - error.config.metadata.startTime;
      console.error(`API Error: ${error.config.method?.toUpperCase()} ${error.config.url} - ${duration}ms`, error);
    }

    // Handle different error scenarios
    if (error.response) {
      // Server responded with error status
      const { status, data } = error.response;

      switch (status) {
        case 401:
          // Unauthorized - clear token and redirect to login
          localStorage.removeItem('token');
          delete api.defaults.headers.common['Authorization'];
          
          if (window.location.pathname !== '/login') {
            toast.error('Session expired. Please login again.');
            window.location.href = '/login';
          }
          break;

        case 403:
          // Forbidden
          toast.error('Access denied. You don\'t have permission to perform this action.');
          break;

        case 404:
          // Not found
          toast.error('Resource not found.');
          break;

        case 422:
          // Validation error
          if (data.error?.message) {
            toast.error(data.error.message);
          } else {
            toast.error('Validation error. Please check your input.');
          }
          break;

        case 429:
          // Rate limit exceeded
          toast.error('Too many requests. Please try again later.');
          break;

        case 500:
          // Server error
          toast.error('Server error. Please try again later.');
          break;

        default:
          // Other errors
          const errorMessage = data.error?.message || `Request failed with status ${status}`;
          toast.error(errorMessage);
      }
    } else if (error.request) {
      // Network error
      console.error('Network error:', error.request);
      toast.error('Network error. Please check your connection.');
    } else {
      // Other error
      console.error('Request error:', error.message);
      toast.error('An unexpected error occurred.');
    }

    return Promise.reject(error);
  }
);

// API methods
export const authAPI = {
  login: (credentials) => api.post('/auth/login', credentials),
  register: (userData) => api.post('/auth/register', userData),
  logout: () => api.post('/auth/logout'),
  refreshToken: () => api.post('/auth/refresh'),
  getCurrentUser: () => api.get('/auth/me'),
  verifyEmail: (token) => api.post('/auth/verify-email', { token }),
};

export const tasksAPI = {
  getTasks: (params) => api.get('/tasks', { params }),
  getTask: (id) => api.get(`/tasks/${id}`),
  createTask: (taskData) => api.post('/tasks', taskData),
  updateTask: (id, taskData) => api.put(`/tasks/${id}`, taskData),
  deleteTask: (id) => api.delete(`/tasks/${id}`),
  startTask: (id) => api.post(`/tasks/${id}/start`),
  completeTask: (id, data) => api.post(`/tasks/${id}/complete`, data),
  scheduleTask: (id) => api.post(`/tasks/${id}/schedule`),
  batchSchedule: () => api.post('/tasks/batch-schedule'),
};

export const analyticsAPI = {
  getProductivityMetrics: (params) => api.get('/analytics/productivity', { params }),
  getEnergyPatterns: (params) => api.get('/analytics/energy-patterns', { params }),
  getTaskCompletionStats: (params) => api.get('/analytics/task-completion', { params }),
  getUserBehavior: (params) => api.get('/analytics/user-behavior', { params }),
  getInsights: () => api.get('/analytics/insights'),
};

export const integrationsAPI = {
  getIntegrations: () => api.get('/integrations'),
  connectIntegration: (service, data) => api.post(`/integrations/${service}/connect`, data),
  disconnectIntegration: (service) => api.delete(`/integrations/${service}/disconnect`),
  syncIntegration: (service) => api.post(`/integrations/${service}/sync`),
  getIntegrationStatus: (service) => api.get(`/integrations/${service}/status`),
};

export const voiceAPI = {
  transcribeAudio: (audioFile) => {
    const formData = new FormData();
    formData.append('audio', audioFile);
    return api.post('/voice/transcribe', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
  },
  processVoiceCommand: (text) => api.post('/voice/process', { text }),
};

export const userAPI = {
  getProfile: () => api.get('/user/profile'),
  updateProfile: (data) => api.put('/user/profile', data),
  getSettings: () => api.get('/user/settings'),
  updateSettings: (data) => api.put('/user/settings', data),
  uploadAvatar: (file) => {
    const formData = new FormData();
    formData.append('avatar', file);
    return api.post('/user/avatar', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
  },
};

export default api;
