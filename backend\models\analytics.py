"""
Analytics and metrics database models.
"""

from sqlalchemy import (
    Column, String, Boolean, DateTime, Text, Integer, 
    ForeignKey, JSON, Float, Date
)
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
import uuid
from app.database import Base


class ProductivityMetric(Base):
    """Daily productivity metrics for users."""
    
    __tablename__ = "productivity_metrics"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    user_id = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=False)
    
    # Date for metrics
    date = Column(Date, nullable=False)
    
    # Task metrics
    tasks_created = Column(Integer, default=0)
    tasks_completed = Column(Integer, default=0)
    tasks_cancelled = Column(Integer, default=0)
    completion_rate = Column(Float, default=0.0)
    
    # Time metrics
    total_time_planned = Column(Integer, default=0)  # minutes
    total_time_spent = Column(Integer, default=0)    # minutes
    average_task_duration = Column(Float, default=0.0)
    
    # Focus metrics
    focus_sessions = Column(Integer, default=0)
    focus_time = Column(Integer, default=0)  # minutes
    break_time = Column(Integer, default=0)  # minutes
    
    # Energy metrics
    peak_productivity_hour = Column(Integer)  # 0-23
    low_productivity_hour = Column(Integer)   # 0-23
    energy_score = Column(Float, default=0.0)  # 0-10
    
    # Overall scores
    productivity_score = Column(Float, default=0.0)  # 0-10
    efficiency_score = Column(Float, default=0.0)    # 0-10
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    user = relationship("User", back_populates="productivity_metrics")
    
    def __repr__(self):
        return f"<ProductivityMetric(user_id={self.user_id}, date={self.date}, score={self.productivity_score})>"


class EnergyPattern(Base):
    """User energy patterns and preferences."""
    
    __tablename__ = "energy_patterns"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    user_id = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=False)
    
    # Pattern identification
    pattern_date = Column(Date, nullable=False)
    day_of_week = Column(Integer, nullable=False)  # 0=Monday, 6=Sunday
    
    # Hourly energy levels (0-10 scale)
    energy_levels = Column(JSON, nullable=False)  # 24-hour array
    
    # Activity correlation
    high_energy_activities = Column(JSON)  # Activities done during high energy
    low_energy_activities = Column(JSON)   # Activities done during low energy
    
    # Environmental factors
    weather_condition = Column(String(50))
    temperature = Column(Float)
    sleep_hours = Column(Float)
    sleep_quality = Column(Integer)  # 1-10 scale
    
    # Lifestyle factors
    caffeine_intake = Column(Integer)  # mg
    exercise_minutes = Column(Integer)
    stress_level = Column(Integer)     # 1-10 scale
    
    # Pattern confidence
    confidence_score = Column(Float, default=0.0)  # 0-1 scale
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # Relationships
    user = relationship("User", back_populates="energy_patterns")
    
    def __repr__(self):
        return f"<EnergyPattern(user_id={self.user_id}, date={self.pattern_date})>"


class TaskCompletion(Base):
    """Detailed task completion tracking."""
    
    __tablename__ = "task_completions"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    task_id = Column(UUID(as_uuid=True), ForeignKey("tasks.id"), nullable=False)
    user_id = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=False)
    
    # Completion details
    started_at = Column(DateTime(timezone=True), nullable=False)
    completed_at = Column(DateTime(timezone=True), nullable=False)
    duration_minutes = Column(Integer, nullable=False)
    
    # Performance metrics
    estimated_duration = Column(Integer)  # minutes
    actual_vs_estimated_ratio = Column(Float)
    interruptions_count = Column(Integer, default=0)
    
    # Context information
    completion_hour = Column(Integer)  # 0-23
    day_of_week = Column(Integer)      # 0=Monday, 6=Sunday
    energy_level_at_start = Column(Integer)  # 1-10 scale
    energy_level_at_end = Column(Integer)    # 1-10 scale
    
    # Quality metrics
    quality_rating = Column(Integer)   # 1-10 scale (self-reported)
    satisfaction_rating = Column(Integer)  # 1-10 scale (self-reported)
    
    # Environmental context
    location = Column(String(100))     # home, office, cafe, etc.
    device_used = Column(String(50))   # desktop, mobile, tablet
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # Relationships
    task = relationship("Task", back_populates="completions")
    user = relationship("User")
    
    def __repr__(self):
        return f"<TaskCompletion(task_id={self.task_id}, duration={self.duration_minutes}min)>"


class UserBehavior(Base):
    """User behavior patterns and insights."""
    
    __tablename__ = "user_behaviors"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    user_id = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=False)
    
    # Behavior tracking period
    period_start = Column(Date, nullable=False)
    period_end = Column(Date, nullable=False)
    period_type = Column(String(20), nullable=False)  # daily, weekly, monthly
    
    # Task creation patterns
    avg_tasks_per_day = Column(Float, default=0.0)
    peak_creation_hour = Column(Integer)  # 0-23
    preferred_task_duration = Column(Integer)  # minutes
    
    # Work patterns
    preferred_work_hours = Column(JSON)  # Array of preferred hours
    most_productive_day = Column(Integer)  # 0=Monday, 6=Sunday
    least_productive_day = Column(Integer)
    
    # Completion patterns
    avg_completion_rate = Column(Float, default=0.0)
    procrastination_score = Column(Float, default=0.0)  # 0-10 scale
    consistency_score = Column(Float, default=0.0)      # 0-10 scale
    
    # Preference patterns
    preferred_categories = Column(JSON)  # Most used categories
    preferred_priorities = Column(JSON)  # Priority distribution
    common_tags = Column(JSON)          # Most used tags
    
    # Insights and recommendations
    insights = Column(JSON)             # AI-generated insights
    recommendations = Column(JSON)      # AI-generated recommendations
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    user = relationship("User")
    
    def __repr__(self):
        return f"<UserBehavior(user_id={self.user_id}, period={self.period_type})>"
