version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: flowmind-postgres
    environment:
      POSTGRES_DB: flowmind
      POSTGRES_USER: flowmind_user
      POSTGRES_PASSWORD: flowmind_password
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init-db.sql:/docker-entrypoint-initdb.d/init-db.sql
    ports:
      - "5432:5432"
    networks:
      - flowmind-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U flowmind_user -d flowmind"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: flowmind-redis
    command: redis-server --appendonly yes
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    networks:
      - flowmind-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Backend API
  backend:
    build:
      context: ..
      dockerfile: docker/Dockerfile.backend
    container_name: flowmind-backend
    environment:
      - DATABASE_URL=**********************************************************/flowmind
      - REDIS_URL=redis://redis:6379/0
      - JWT_SECRET_KEY=your-super-secret-jwt-key-change-this-in-production
      - DEBUG=false
      - CORS_ORIGINS=http://localhost:3000,http://frontend:3000
    ports:
      - "8000:8000"
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - flowmind-network
    volumes:
      - ../backend:/app
      - backend_logs:/app/logs
    restart: unless-stopped

  # Frontend Web App
  frontend:
    build:
      context: ..
      dockerfile: docker/Dockerfile.frontend
    container_name: flowmind-frontend
    environment:
      - REACT_APP_API_URL=http://localhost:8000
      - REACT_APP_WS_URL=ws://localhost:8000
    ports:
      - "3000:3000"
    depends_on:
      - backend
    networks:
      - flowmind-network
    restart: unless-stopped

  # Celery Worker (Background Tasks)
  celery-worker:
    build:
      context: ..
      dockerfile: docker/Dockerfile.backend
    container_name: flowmind-celery-worker
    command: celery -A app.celery worker --loglevel=info
    environment:
      - DATABASE_URL=**********************************************************/flowmind
      - REDIS_URL=redis://redis:6379/0
      - CELERY_BROKER_URL=redis://redis:6379/1
      - CELERY_RESULT_BACKEND=redis://redis:6379/2
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - flowmind-network
    volumes:
      - ../backend:/app
      - worker_logs:/app/logs
    restart: unless-stopped

  # Celery Beat (Scheduled Tasks)
  celery-beat:
    build:
      context: ..
      dockerfile: docker/Dockerfile.backend
    container_name: flowmind-celery-beat
    command: celery -A app.celery beat --loglevel=info
    environment:
      - DATABASE_URL=**********************************************************/flowmind
      - REDIS_URL=redis://redis:6379/0
      - CELERY_BROKER_URL=redis://redis:6379/1
      - CELERY_RESULT_BACKEND=redis://redis:6379/2
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - flowmind-network
    volumes:
      - ../backend:/app
      - beat_logs:/app/logs
    restart: unless-stopped

  # Nginx Reverse Proxy (Production)
  nginx:
    image: nginx:alpine
    container_name: flowmind-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - frontend
      - backend
    networks:
      - flowmind-network
    restart: unless-stopped
    profiles:
      - production

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  backend_logs:
    driver: local
  worker_logs:
    driver: local
  beat_logs:
    driver: local

networks:
  flowmind-network:
    driver: bridge
